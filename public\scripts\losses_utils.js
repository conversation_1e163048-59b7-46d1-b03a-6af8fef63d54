// public/scripts/losses_utils.js

export const formatVal = (val, precision = 2, unit = '') => {
    if (val === null || val === undefined || (typeof val === 'number' && isNaN(val))) return '-';
    const num = Number(val);
    if (isNaN(num)) return typeof val === 'string' && val.trim() !== "" ? val : '-';
    return num.toFixed(precision) + unit;
};

export function getStatusClass(statusString) {
    if (!statusString) return '';
    if (statusString.includes('CRÍTICO') || statusString.includes('EPS V≤ > Limite') || statusString.includes('EPS V> > Limite') || statusString.includes('EPS > Limite') || statusString.includes('P_DUT > Limite')) return 'status-critical-red';
    if (statusString.includes('ALERTA') || statusString.includes('Vteste V> > Limite') || statusString.includes('Vteste > Vmax Banco') || statusString.includes('G1 insuf.')) return 'status-alert-orange';
    if (statusString.includes('OK')) return 'status-ok-green';
    return '';
}

export function getStatusWithIcon(statusString) {
    if (!statusString) return 'N/A';
    if (statusString.includes('CRÍTICO') || statusString.includes('EPS V≤ > Limite') || statusString.includes('EPS V> > Limite') || statusString.includes('EPS > Limite') || statusString.includes('P_DUT > Limite')) return `🚨 ${statusString}`;
    if (statusString.includes('ALERTA') || statusString.includes('Vteste V> > Limite') || statusString.includes('Vteste > Vmax Banco') || statusString.includes('G1 insuf.')) return `⚠️ ${statusString}`;
    if (statusString.includes('OK')) return `✅ ${statusString}`;
    if (statusString.includes('Potência Subcompensada') || statusString.includes('Subcompensada')) return `ℹ️ ${statusString}`;
    return statusString;
}

export function getSutEpsClass(current_eps_a, eps_limit_pos, eps_limit_neg) {
    if (current_eps_a === null || current_eps_a === undefined || isNaN(current_eps_a)) return 'sut-eps-na';
    if (current_eps_a > eps_limit_pos + 1e-6 || current_eps_a < eps_limit_neg - 1e-6) return 'sut-eps-critical';
    if (current_eps_a < -1e-6) return 'sut-eps-excessive';
    const percent_from_pos_limit = (current_eps_a / eps_limit_pos) * 100;
    if (percent_from_pos_limit < 50) return 'sut-eps-normal';
    if (percent_from_pos_limit < 85) return 'sut-eps-alert';
    return 'sut-eps-high';
}

export function getStatusIcon(current_eps_a, eps_limit_pos, eps_limit_neg) {
    if (current_eps_a === null || current_eps_a === undefined || isNaN(current_eps_a)) return '<i class="fas fa-question-circle text-muted ms-1" title="N/A"></i>';
    if (current_eps_a > eps_limit_pos + 1e-6 || current_eps_a < eps_limit_neg - 1e-6) return '<i class="fas fa-times-circle text-danger ms-1" title="Crítico (Fora dos limites EPS)"></i>';
    if (current_eps_a < -1e-6) return '<i class="fas fa-arrow-alt-circle-down text-info ms-1" title="Corrente Capacitiva"></i>';
    const percent_from_pos_limit = (current_eps_a / eps_limit_pos) * 100;
    if (percent_from_pos_limit < 50) return '<i class="fas fa-check-circle text-success ms-1" title="Normal"></i>';
    if (percent_from_pos_limit < 85) return '<i class="fas fa-exclamation-circle text-warning ms-1" title="Alerta"></i>';
    return '<i class="fas fa-exclamation-triangle text-orange ms-1" title="Alto"></i>';
}

export function isQEfetivaExceedingPteste(qEfetivaMvar, pTesteMva) {
    if (qEfetivaMvar === null || qEfetivaMvar === undefined || typeof qEfetivaMvar !== 'number' || isNaN(qEfetivaMvar) || qEfetivaMvar <= 0) return false;
    if (pTesteMva === null || pTesteMva === undefined || typeof pTesteMva !== 'number' || isNaN(pTesteMva) || pTesteMva <= 0) return false;
    return qEfetivaMvar > pTesteMva + 0.001;
}

export function isVtestExceedingBankLimit(vtestKv, bankVoltageKv, factorOvervoltage) {
    if (!vtestKv || !bankVoltageKv || !factorOvervoltage) return false;
    const limitKv = bankVoltageKv * factorOvervoltage;
    return vtestKv > limitKv + 0.001;
}

export function isPatvExceedingDutLimit(patvKw, dutLimitKw = 1350.0) {
    if (!patvKw || !dutLimitKw) return false;
    return patvKw > dutLimitKw + 0.001;
}

export function isItestExceedingCtLimit(itestA, ctLimitA = 2000.0) {
    if (!itestA || !ctLimitA) return false;
    return itestA > ctLimitA + 0.001;
}

export function isVtestExceedingSutLimit(vtestKv, sutMaxKv = 140.0) {
    if (!vtestKv || !sutMaxKv) return false;
    return vtestKv > sutMaxKv + 0.001;
}

// Function to determine the MAX overall capacitor bank voltage from limitsInfo
export function getMaxOverallCapBankVoltage(limitsInfo) {
    let maxVoltage = 0;
    if (limitsInfo?.capacitor_power_limits_by_voltage) {
        const voltageKeys = Object.keys(limitsInfo.capacitor_power_limits_by_voltage);
        if (voltageKeys.length > 0) {
            maxVoltage = Math.max(...voltageKeys.map(k => parseFloat(k)).filter(v => !isNaN(v) && v > 0));
        }
    }
    return maxVoltage;
}

// Helper to get parameters for a single scenario based on current radio selections
export function getScenarioParametersForStatusUpdate(tapIndexGlobal, scenIndexOriginal, cenario, limitsInfo, basicData, factorOvervoltage) {
    const { test_params_cenario } = cenario;
    const vTestKv = test_params_cenario.tensao_kv;
    const iTestA = test_params_cenario.corrente_a;
    const pativaKw = test_params_cenario.pativa_kw;
    const qTesteMvar = test_params_cenario.q_teste_mvar;
    const sqrt3_factor = (basicData.tipo_transformador.toLowerCase() === 'trifásico') ? Math.sqrt(3) : 1.0;

    const sfRadio = document.querySelector(`input[name="bank-option-sf-${tapIndexGlobal}-${scenIndexOriginal}"]:checked`);
    const cfRadio = document.querySelector(`input[name="bank-option-cf-${tapIndexGlobal}-${scenIndexOriginal}"]:checked`);
    const sfConfigIndex = sfRadio ? parseInt(sfRadio.dataset.configIndex) : -1;
    const cfConfigIndex = cfRadio ? parseInt(cfRadio.dataset.configIndex) : -1;

    const sfAvailableConfigs = cenario.cap_bank_sf?.available_configurations || [];
    const cfAvailableConfigs = cenario.cap_bank_cf?.available_configurations || [];
    const fallbackConfig = { q_efetiva_banco_mvar: 0.0, meets_ideal_power_req: false, eps_within_limits_estimation: false, eps_within_limits: false };

    const activeSfConfig = (sfConfigIndex !== -1 && sfAvailableConfigs.length > sfConfigIndex) ? sfAvailableConfigs[sfConfigIndex] : fallbackConfig;
    const activeCfConfig = (cfConfigIndex !== -1 && cfAvailableConfigs.length > cfConfigIndex) ? cfAvailableConfigs[cfConfigIndex] : fallbackConfig;
    
    const currentQEffSf = activeSfConfig.q_efetiva_banco_mvar;
    const currentQEffCf = activeCfConfig.q_efetiva_banco_mvar;

    const cfBankIsEffectivelyAvailable = cfAvailableConfigs.length > 0 && !(cfAvailableConfigs.length === 1 && cfAvailableConfigs[0].q_config && cfAvailableConfigs[0].q_config.startsWith("N/A"));
    
    let isEpsSfOkActualForIdealSut = activeSfConfig.eps_within_limits_estimation || activeSfConfig.eps_within_limits;
    let isEpsCfOkActualForIdealSut = cfBankIsEffectivelyAvailable ? (activeCfConfig.eps_within_limits_estimation || activeCfConfig.eps_within_limits) : true;

    let idealSutTapKvForThisScenario = null;
    if (cenario.sut_eps_analysis && Array.isArray(cenario.sut_eps_analysis) && cenario.sut_eps_analysis.length > 0) {
        const idealEntry = cenario.sut_eps_analysis.find(sutEntry => sutEntry.is_ideal_tap === true);
        if (idealEntry && typeof idealEntry.sut_tap_kv === 'number') {
            idealSutTapKvForThisScenario = idealEntry.sut_tap_kv;
        } else { 
            const targetSutRefV = vTestKv * 1000.0;
            let localSutTaps = Array.from(new Set(cenario.sut_eps_analysis.map(s => s.sut_tap_kv).filter(kv => kv !== undefined && typeof kv === 'number'))).sort((a,b) => a - b);
            if(localSutTaps.length > 0) {
                const tapsGteVtest = localSutTaps.filter(tapKv => (tapKv*1000) >= targetSutRefV - 1e-6);
                if(tapsGteVtest.length > 0) idealSutTapKvForThisScenario = Math.min(...tapsGteVtest);
                else idealSutTapKvForThisScenario = Math.max(...localSutTaps);
            }
        }
    }

    if (idealSutTapKvForThisScenario !== null && vTestKv > 0 && limitsInfo.eps_current_limit_negative_a !== undefined) {
        const SUT_BT_VOLTAGE = 480; // Should be from constants
        const ratioSut = (SUT_BT_VOLTAGE > 1e-6) ? (idealSutTapKvForThisScenario * 1000) / SUT_BT_VOLTAGE : 0;
        
        const iCapSfForIdealSUT = (vTestKv * sqrt3_factor > 1e-6 && currentQEffSf > 1e-6) ? (currentQEffSf * 1000) / (vTestKv * sqrt3_factor) : 0;
        let sfCurrentIdeal = (iTestA - iCapSfForIdealSUT) * ratioSut;
        if (basicData.tipo_transformador.toLowerCase() === 'monofásico') sfCurrentIdeal *= Math.sqrt(3);
        isEpsSfOkActualForIdealSut = (sfCurrentIdeal >= limitsInfo.eps_current_limit_negative_a - 1e-6 && sfCurrentIdeal <= limitsInfo.eps_current_limit_positive_a + 1e-6);

        if (cfBankIsEffectivelyAvailable) {
            const iCapCfForIdealSUT = (vTestKv * sqrt3_factor > 1e-6 && currentQEffCf > 1e-6) ? (currentQEffCf * 1000) / (vTestKv * sqrt3_factor) : 0;
            let cfCurrentIdeal = (iTestA - iCapCfForIdealSUT) * ratioSut;
            if (basicData.tipo_transformador.toLowerCase() === 'monofásico') cfCurrentIdeal *= Math.sqrt(3);
            isEpsCfOkActualForIdealSut = (cfCurrentIdeal >= limitsInfo.eps_current_limit_negative_a - 1e-6 && cfCurrentIdeal <= limitsInfo.eps_current_limit_positive_a + 1e-6);
        } else {
            isEpsCfOkActualForIdealSut = true;
        }
    }

    return {
        testVoltageKv: vTestKv,
        iTestA: iTestA,
        qTesteMvar: qTesteMvar,
        activePowerKw: pativaKw,
        factorCapBancOvervoltage: factorOvervoltage,
        sfBankNominalKv: cenario.cap_bank_sf?.tensao_disp_kv,
        sfEpsOk: isEpsSfOkActualForIdealSut,
        sfPowerIdealMet: activeSfConfig.meets_ideal_power_req,
        sfGroupInfo: cenario.cap_bank_sf?.group_info,
        sfAvailableConfigs: sfAvailableConfigs,
        cfBankNominalKv: cenario.cap_bank_cf?.tensao_disp_kv,
        cfEpsOk: isEpsCfOkActualForIdealSut,
        cfPowerIdealMet: activeCfConfig.meets_ideal_power_req,
        cfGroupInfo: cenario.cap_bank_cf?.group_info,
        cfAvailableConfigs: cfAvailableConfigs,
        cfActive: cfBankIsEffectivelyAvailable,
        limitsInfo: limitsInfo
    };
}

// Replicates Python's get_detailed_scenario_status
export function getDetailedScenarioStatusJS(params) {
    const {
        testVoltageKv, iTestA, qTesteMvar, activePowerKw, factorCapBancOvervoltage,
        sfBankNominalKv, sfEpsOk, sfPowerIdealMet, sfGroupInfo, sfAvailableConfigs,
        cfBankNominalKv, cfEpsOk: cfEpsOkParam, cfPowerIdealMet: cfPowerIdealMetParam, cfGroupInfo, cfAvailableConfigs, cfActive,
        limitsInfo
    } = params;

    const epsilon = 1e-6;
    let status_v_menor_parts = [];
    let status_v_maior_parts = [];
    let status_global_parts = [];
    let cap_required_mvar_v_menor = null;
    let cap_required_mvar_v_maior = null;

    // --- V≤ (SF Bank) Logic ---
    if (sfBankNominalKv && qTesteMvar > epsilon && testVoltageKv > epsilon) {
        const qBancoNecessariaSf = qTesteMvar * Math.pow(sfBankNominalKv / testVoltageKv, 2);
        cap_required_mvar_v_menor = qBancoNecessariaSf;
        const voltageLimitsSf = limitsInfo?.capacitor_power_limits_by_voltage?.[sfBankNominalKv.toString()];
        if (voltageLimitsSf) {
            const grupo1MaxSf = voltageLimitsSf.grupo1?.max;
            const grupo1_2MaxSf = voltageLimitsSf.grupo1_2?.max;
            if (grupo1_2MaxSf !== undefined && qBancoNecessariaSf > grupo1_2MaxSf + epsilon) {
                status_v_menor_parts.push(`Q_req V≤ CRÍTICO (${qBancoNecessariaSf.toFixed(1)} > ${grupo1_2MaxSf.toFixed(1)} MVAr)`);
            } else if (grupo1MaxSf !== undefined && qBancoNecessariaSf > grupo1MaxSf + epsilon) {
                status_v_menor_parts.push(`G1 insuf. V≤, req G1+G2 (${qBancoNecessariaSf.toFixed(1)} MVAr)`);
            } else if (sfGroupInfo && sfGroupInfo.includes("Grupo 1+2")) {
                // Se o grupo efetivamente selecionado foi G1+G2, mesmo que a potência necessária seja <= G1 max,
                // isso indica que G1 sozinho não foi adequado (por questões de EPS ou outras limitações)
                status_v_menor_parts.push(`G1 insuf. V≤, req G1+G2 (${qBancoNecessariaSf.toFixed(1)} MVAr)`);
            }
        }
    }
    
    const vMenorHasEpsOkConfig = sfEpsOk; // This now directly reflects the selected config's EPS status at ideal SUT
    const vMaiorHasEpsOkConfigForVMenorCheck = cfActive ? cfEpsOk : true; // If CF not active, assume it's "OK" for this check
    
    if (!vMenorHasEpsOkConfig && !vMaiorHasEpsOkConfigForVMenorCheck) {
        status_v_menor_parts.push("EPS V≤ > Limite");
    } else if (!sfPowerIdealMet && qTesteMvar > epsilon) {
        status_v_menor_parts.push("Subcompensada V≤");
    }

    // --- V> (CF Bank) Logic ---
    if (cfActive) {
        if (cfBankNominalKv && qTesteMvar > epsilon && testVoltageKv > epsilon) {
            const qBancoNecessariaCf = qTesteMvar * Math.pow(cfBankNominalKv / testVoltageKv, 2);
            cap_required_mvar_v_maior = qBancoNecessariaCf;
            const voltageLimitsCf = limitsInfo?.capacitor_power_limits_by_voltage?.[cfBankNominalKv.toString()];
            if (voltageLimitsCf) {
                const grupo1MaxCf = voltageLimitsCf.grupo1?.max;
                const grupo1_2MaxCf = voltageLimitsCf.grupo1_2?.max;
                if (grupo1_2MaxCf !== undefined && qBancoNecessariaCf > grupo1_2MaxCf + epsilon) {
                    status_v_maior_parts.push(`Q_req V> CRÍTICO (${qBancoNecessariaCf.toFixed(1)} > ${grupo1_2MaxCf.toFixed(1)} MVAr)`);
                } else if (grupo1MaxCf !== undefined && qBancoNecessariaCf > grupo1MaxCf + epsilon) {
                    status_v_maior_parts.push(`G1 insuf. V>, req G1+G2 (${qBancoNecessariaCf.toFixed(1)} MVAr)`);
                } else if (cfGroupInfo && cfGroupInfo.includes("Grupo 1+2")) {
                    // Se o grupo efetivamente selecionado foi G1+G2, mesmo que a potência necessária seja <= G1 max,
                    // isso indica que G1 sozinho não foi adequado (por questões de EPS ou outras limitações)
                    status_v_maior_parts.push(`G1 insuf. V>, req G1+G2 (${qBancoNecessariaCf.toFixed(1)} MVAr)`);
                }
            }
        }
        
        const vMaiorHasEpsOkConfigLocal = cfEpsOkParam; // Reflects selected CF config's EPS at ideal SUT
        const vMenorHasEpsOkConfigForVMaiorCheck = sfEpsOk; // Reflects selected SF config's EPS at ideal SUT

        if (!vMaiorHasEpsOkConfigLocal && !vMenorHasEpsOkConfigForVMaiorCheck) {
            status_v_maior_parts.push("EPS V> > Limite");
        } else if (!cfPowerIdealMetParam && qTesteMvar > epsilon) {
            status_v_maior_parts.push("Subcompensada V>");
        }
    }

    // --- Global Logic ---
    const maxOverallCapVoltage = getMaxOverallCapBankVoltage(limitsInfo);
    if (maxOverallCapVoltage > 0) {
        // Apply overvoltage factor to the maximum bank voltage for global validation
        const maxOverallCapVoltageWithFactor = maxOverallCapVoltage * factorCapBancOvervoltage;
        if (testVoltageKv > maxOverallCapVoltageWithFactor + epsilon) {
            status_global_parts.push(`Vteste > Vmax Banco (${maxOverallCapVoltage.toFixed(1)}kV)`);
        }
    }
    const dutPowerLimit = limitsInfo?.dut_power_limit_kw || 1350.0;
    if (activePowerKw > dutPowerLimit + epsilon) {
        status_global_parts.push(`P_DUT > Limite (${dutPowerLimit.toFixed(0)}kW)`);
    }

    // CT Current Limit validation
    const ctCurrentLimit = limitsInfo?.ct_current_limit_a || 2000.0;
    if (iTestA > ctCurrentLimit + epsilon) {
        status_global_parts.push(`🚨 CRÍTICO: Itest > CT (${ctCurrentLimit.toFixed(0)}A)`);
    }

    // SUT Voltage Limit validation
    const sutMaxVoltage = limitsInfo?.sut_at_max_voltage_kv || 140.0;
    if (testVoltageKv > sutMaxVoltage + epsilon) {
        status_global_parts.push(`🚨 CRÍTICO: Vtest > SUT (${sutMaxVoltage.toFixed(0)}kV)`);
    }
    
    const cap_required_max = Math.max(cap_required_mvar_v_menor || 0, cap_required_mvar_v_maior || 0);
    if (status_v_menor_parts.some(s => s.includes("Q_req V≤ CRÍTICO")) || (cfActive && status_v_maior_parts.some(s => s.includes("Q_req V> CRÍTICO")))) {
        if (cap_required_max > epsilon && !status_global_parts.some(s => s.includes("Potência necessária"))) {
             // status_global_parts.push(`Potência necessária: ${cap_required_max.toFixed(1)} MVAr`);
        }
    }
    
    return {
        status_v_menor: status_v_menor_parts.length > 0 ? status_v_menor_parts.join(" | ") : "OK",
        status_v_maior: cfActive ? (status_v_maior_parts.length > 0 ? status_v_maior_parts.join(" | ") : "OK") : "N/A",
        status_global: status_global_parts.length > 0 ? status_global_parts.join(" | ") : "OK",
        cap_required_mvar: cap_required_max > epsilon ? cap_required_max : null
    };
}