<!DOCTYPE html>
<html lang="pt-BR">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    /* --- INÍCIO DA REFORMULAÇÃO DO ESTILO --- */

    /* 1. Cores e Fontes Globais */
    :root {
      --cor-fundo-principal: #f4f8fa; /* Cinza muito claro */
      --cor-fundo-slide: #ffffff;
      --cor-fundo-box: #f9fafb;
      --cor-borda-suave: #e7e9ec;
      --cor-texto-principal: #343a40; /* Cinza escuro */
      --cor-texto-secundario: #6c757d; /* Cinza médio */
      --cor-sidebar-fundo: #2c3e50; /* Carvão / Azul ardósia */
      --cor-sidebar-texto: #ecf0f1; /* Cinza claro */
      --cor-primaria: #3498db; /* Azul suave */
      --cor-sucesso: #28a745;
      --cor-alerta: #ffc107;
      --cor-perigo: #dc3545;
      --cor-info: #17a2b8;
      --sidebar-width: 300px; /* Variável para a largura do sidebar */
    }

    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      background-color: var(--cor-fundo-principal);
      color: var(--cor-texto-principal);
      scroll-behavior: smooth;
      overflow-x: hidden; /* Prevenir scroll horizontal */
      transition: padding-left 0.3s ease-in-out;
    }

    /* 2. Sidebar (Navegação) */
    .sidebar {
      width: var(--sidebar-width);
      background: var(--cor-sidebar-fundo);
      color: var(--cor-sidebar-texto);
      padding: 20px;
      height: 100vh;
      position: fixed;
      top: 0;
      left: 0;
      overflow-y: auto;
      border-right: 1px solid #34495e;
      z-index: 1010; /* Z-index maior para ficar sobre o botão */
      box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease-in-out;
      transform: translateX(0);
    }
    
    /* ESTADO COLAPSADO DO SIDEBAR */
    body.sidebar-collapsed .sidebar {
        transform: translateX(calc(-1 * var(--sidebar-width)));
    }

    .sidebar-title {
      font-size: 22px;
      font-weight: 700;
      margin-bottom: 25px;
      text-align: center;
      color: #ffffff;
      padding-bottom: 15px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    .sidebar-search input {
      width: 100%;
      padding: 10px 12px;
      margin-bottom: 20px;
      border-radius: 6px;
      border: 1px solid #4a627a;
      background-color: #34495e;
      color: var(--cor-sidebar-texto);
      font-size: 14px;
      transition: all 0.3s;
    }
    .sidebar-search input::placeholder { color: #95a5a6; }
    .sidebar-search input:focus {
      border-color: var(--cor-primaria);
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3);
      outline: none;
    }
    .sidebar ul { list-style-type: none; padding: 0; }
    .sidebar ul li a {
      display: block;
      color: #bdc3c7;
      padding: 10px 15px;
      text-decoration: none;
      border-radius: 5px;
      margin-bottom: 5px;
      transition: all 0.2s ease;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .sidebar ul li a:hover {
      background-color: #34495e;
      color: white;
      transform: translateX(4px);
    }
    .sidebar ul li a.active {
      background-color: var(--cor-primaria);
      color: white;
      font-weight: 600;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    .sidebar ul li a.active-search-result {
      background-color: var(--cor-alerta) !important;
      color: #333 !important;
      font-weight: 600;
    }
    .sidebar .nav-section-title {
      font-size: 12px;
      text-transform: uppercase;
      color: #95a5a6;
      margin-top: 20px;
      margin-bottom: 8px;
      padding-left: 15px;
      font-weight: 600;
    }
    
    /* 3. Conteúdo Principal e Slides */
    .main-content {
      margin-left: var(--sidebar-width);
      width: calc(100% - var(--sidebar-width));
      padding: 30px;
      height: 100vh;
      overflow-y: auto;
      overflow-x: hidden;
      transition: margin-left 0.3s ease-in-out, width 0.3s ease-in-out;
    }
    
    /* ESTADO COLAPSADO DO CONTEÚDO PRINCIPAL */
    body.sidebar-collapsed .main-content {
        margin-left: 0;
        width: 100%;
    }

    .slide-container {
      width: 100%;
      max-width: 1280px;
      min-height: 720px;
      background: var(--cor-fundo-slide);
      color: var(--cor-texto-principal);
      padding: 40px 50px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      margin-bottom: 30px;
      border-radius: 12px;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.07);
      border: 1px solid var(--cor-borda-suave);
      overflow: hidden;
    }
    .slide-title {
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 25px;
      color: var(--cor-texto-principal);
      border-bottom: 2px solid var(--cor-primaria);
      padding-bottom: 15px;
      text-align: center;
    }
    .slide-subtitle {
      font-size: 22px;
      margin-bottom: 20px;
      color: var(--cor-primaria);
      font-weight: 600;
    }
    
    /* BOTÃO DE TOGGLE DO SIDEBAR */
    #sidebar-toggle {
        position: fixed;
        top: 15px;
        left: 15px;
        z-index: 1020; /* Maior que o sidebar para ser clicável */
        background-color: var(--cor-sidebar-fundo);
        color: var(--cor-sidebar-texto);
        border: 1px solid #4a627a;
        border-radius: 50%;
        width: 45px;
        height: 45px;
        font-size: 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.3s ease-in-out, box-shadow 0.2s;
    }
    #sidebar-toggle:hover {
        box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
        transform: scale(1.1);
    }
    body:not(.sidebar-collapsed) #sidebar-toggle {
        transform: translateX(var(--sidebar-width));
    }

    /* 4. Componentes de Conteúdo (Caixas, Fórmulas, etc.) */
    .content-section { margin-bottom: 20px; font-size: 16px; line-height: 1.7; }
    .content-container { display: flex; flex: 1; gap: 30px; }
    .left-section, .right-section, .single-column-section { flex: 1; display: flex; flex-direction: column; }
    .info-box, .formula-box, .key-point, .flowchart-container, .table-container, .chart-container, .result-box, .highlight-box, .logic-steps {
      background-color: var(--cor-fundo-box);
      border: 1px solid var(--cor-borda-suave);
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      font-size: 15px;
    }
    .section-title-box {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 12px;
      color: var(--cor-primaria);
    }
    .formula {
      font-family: 'Consolas', 'Courier New', monospace;
      font-size: 15px;
      margin: 10px 0;
      text-align: center;
      background-color: #e9ecef;
      padding: 12px 18px;
      border-radius: 6px;
      color: var(--cor-texto-principal);
      border-left: 4px solid var(--cor-primaria);
      overflow-x: auto;
    }
    .formula-description { font-size: 13px; margin-top: 8px; color: var(--cor-texto-secundario); text-align: center; }
    
    /* 5. Estilos de Texto e Destaques */
    .highlight-text { color: var(--cor-primaria); font-weight: 600; }
    .warning-text { color: #d97706; font-weight: 600; } /* Laranja escuro */
    .success-text { color: var(--cor-sucesso); font-weight: 600; }
    .danger-text { color: var(--cor-perigo); font-weight: 600; }
    .search-highlight { background-color: #fff3cd; color: #664d03; padding: 2px 4px; border-radius: 3px; }

    /* 6. Tabelas */
    table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
    }
    th {
      background-color: #343a40; /* Cabeçalho escuro */
      color: white;
      padding: 12px 15px;
      text-align: left;
      font-weight: 600;
    }
    tr:nth-child(even) { background-color: #f8f9fa; }
    td {
      padding: 12px 15px;
      border-bottom: 1px solid #dee2e6;
      vertical-align: middle;
    }
    th:first-child, td:first-child { border-top-left-radius: 6px; }
    th:last-child, td:last-child { border-top-right-radius: 6px; }
    tr:last-child td:first-child { border-bottom-left-radius: 6px; }
    tr:last-child td:last-child { border-bottom-right-radius: 6px; }
    
    /* 7. Footer e Elementos Específicos do Slide de Título */
    .footer { margin-top: auto; padding-top: 20px; font-size: 13px; color: var(--cor-texto-secundario); text-align: right; font-style: italic; border-top: 1px solid var(--cor-borda-suave); }
    .title-main {
      font-size: 52px;
      font-weight: 700;
      margin-bottom: 20px;
      text-align: center;
      background: linear-gradient(90deg, #2c3e50, #3498db);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .subtitle-main { font-size: 26px; margin-bottom: 40px; text-align: center; color: var(--cor-texto-secundario); }
    .highlight-box-main { background-color: rgba(52, 152, 219, 0.1); border-left: 4px solid var(--cor-primaria); padding: 25px; border-radius: 8px; margin: 30px auto; width: 70%; text-align: center; }
    .footer-main { display: flex; justify-content: space-between; align-items: center; width:100%; margin-top: auto; padding-top: 20px; border-top: 1px solid var(--cor-borda-suave); }
    .logo-main { width: 80px; } 
    .date-main { font-size: 16px; color: var(--cor-texto-secundario);}
    
    /* 8. Estilos de Componentes Diversos (Agenda, Fluxograma, etc.) */
    .agenda-item { display: flex; align-items: center; margin-bottom: 15px; font-size: 15px;}
    .agenda-icon { margin-right: 15px; color: var(--cor-primaria); width: 25px; text-align: center; font-size: 18px;}
    .challenge-content .text-center { background-color: rgba(52, 152, 219, 0.05); border: 1px solid rgba(52, 152, 219, 0.2); padding: 1rem; }
    .challenge-content p { margin-bottom: 1rem; }

    .flowchart-svg .node rect { stroke: var(--cor-primaria); stroke-width: 1.5px; fill: #ffffff; rx: 6; ry: 6; }
    .flowchart-svg .node.decision rect { fill: #eaf5fc; }
    .flowchart-svg .node.action rect { fill: #fff8e1; }
    .flowchart-svg .node.end rect { fill: #fde8e8; }
    .flowchart-svg .node text { fill: var(--cor-texto-principal); font-size: 11px; text-anchor: middle; dominant-baseline: central; }
    .flowchart-svg .edge path { stroke: var(--cor-texto-secundario); stroke-width: 1.5px; fill: none; }
    .flowchart-svg .edge marker path { fill: var(--cor-texto-secundario); }
    .flowchart-svg .edge text { fill: var(--cor-texto-secundario); font-size: 10px; }

    /* 9. --- INICIO DOS ESTILOS DO SIMULADOR (TEMA CLARO) --- */
    .simulator-container { background-color: #e9ecef; padding: 2rem; border-radius: 10px; }
    .simulator-container .card {
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px solid var(--cor-borda-suave);
        color: var(--cor-texto-principal);
        height: 100%;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08); /* Sombra mais pronunciada */
    }
    .simulator-container .card-header {
        font-weight: 600;
        font-size: 0.9rem; /* Tamanho da fonte do cabeçalho */
        color: var(--cor-texto-principal);
        background-color: #f8f9fa;
        border-bottom: 1px solid var(--cor-borda-suave);
        text-align: left; /* Alinhamento do cabeçalho */
    }
    .simulator-container .text-muted { color: var(--cor-texto-secundario) !important; }

    /* --- NOVOS ESTILOS PARA O SIMULADOR DE PERDAS EM VAZIO --- */
    .diagram-vazio-container {
      height: 300px;
    }
    /* CORREÇÃO Z-INDEX: Coloca o container do fio no fundo */
    .vazio-wire-container { 
        position: absolute; 
        top: 45%; /* Posição vertical do fio */
        left: 5%; 
        width: 90%; 
        height: 8px; /* Fio mais espesso */
        z-index: 0; /* Camada de fundo */
        pointer-events: none; /* Permite cliques através dele */
    }
    /* CORREÇÃO Z-INDEX: Coloca os cards na frente */
    #slide-no-load-losses-simulator .col-md-4,
    #slide-no-load-losses-simulator .col-lg-3 {
        position: relative;
        z-index: 1;
    }
    .vazio-wire { 
      width: 100%; 
      height: 100%; 
      background: linear-gradient(to right, #63c463, #8fdd8f); /* Gradiente verde */
      border-radius: 4px; 
    }
    
    /* Estilo para as linhas de dados dentro dos cards do simulador */
    .vazio-data-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
    }
    .vazio-data-row:last-child { margin-bottom: 0; }
    .vazio-data-row label {
        font-size: 0.9rem;
        color: var(--cor-texto-secundario);
    }
    .vazio-data-row .form-control-plaintext {
        max-width: 80px;
        text-align: right;
        padding: 0.25rem 0.5rem;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        background-color: #ffffff;
        font-weight: 500;
        font-family: 'Courier New', Courier, monospace;
    }
    
    .vazio-animated-wire { background-repeat: repeat-x; background-size: 40px 40px; animation: vazio-flow 1.2s linear infinite; }
    .vazio-wire-color-normal { background-image: linear-gradient(45deg, rgba(40, 167, 69, 0.7) 25%, rgba(40, 167, 69, 0.4) 25%, rgba(40, 167, 69, 0.4) 50%, rgba(40, 167, 69, 0.7) 50%, rgba(40, 167, 69, 0.7) 75%, rgba(40, 167, 69, 0.4) 75%, rgba(40, 167, 69, 0.4) 100%); }
    .vazio-wire-color-critical { background-image: linear-gradient(45deg, rgba(220, 53, 69, 0.8) 25%, rgba(220, 53, 69, 0.5) 25%, rgba(220, 53, 69, 0.5) 50%, rgba(220, 53, 69, 0.8) 50%, rgba(220, 53, 69, 0.8) 75%, rgba(220, 53, 69, 0.5) 75%, rgba(220, 53, 69, 0.5) 100%); }
    @keyframes vazio-flow { from { background-position: 0 0; } to { background-position: 40px 0; } }
    @keyframes vazio-flash-border { 0%, 100% { border-color: var(--cor-perigo); box-shadow: 0 0 10px rgba(220, 53, 69, 0.7); } 50% { border-color: var(--cor-borda-suave); box-shadow: none; } }
    .vazio-critical-flash { animation: vazio-flash-border 1s infinite; }
    .card-header.bg-custom-orange {
      background-color: #fd7e14 !important;
      color: white;
    }

    /* Estilos para a Aba "Carga" */
    #carga-tab-pane .display-value { font-size: 1.5rem; min-width: 100px; display: inline-block; text-align: center; }
    .carga-wire { position: absolute; background-color: #adb5bd; z-index: 0; border-radius: 4px; }
    .carga-animated-wire { background-repeat: repeat-x; background-size: 40px 40px; animation: carga-flow 1.2s linear infinite; }
    .carga-animated-wire.reverse { animation-direction: reverse; }
    .carga-wire-color-source { background-image: linear-gradient(45deg, rgba(40, 167, 69, 0.7) 25%, rgba(40, 167, 69, 0.4) 25%, rgba(40, 167, 69, 0.4) 50%, rgba(40, 167, 69, 0.7) 50%, rgba(40, 167, 69, 0.7) 75%, rgba(40, 167, 69, 0.4) 75%, rgba(40, 167, 69, 0.4) 100%); }
    .carga-wire-color-load { background-image: linear-gradient(45deg, rgba(255, 193, 7, 0.8) 25%, rgba(255, 193, 7, 0.5) 25%, rgba(255, 193, 7, 0.5) 50%, rgba(255, 193, 7, 0.8) 50%, rgba(255, 193, 7, 0.8) 75%, rgba(255, 193, 7, 0.5) 75%, rgba(255, 193, 7, 0.5) 100%); }
    .carga-wire-color-cap { background-image: linear-gradient(45deg, rgba(23, 162, 184, 0.7) 25%, rgba(23, 162, 184, 0.4) 25%, rgba(23, 162, 184, 0.4) 50%, rgba(23, 162, 184, 0.7) 50%, rgba(23, 162, 184, 0.7) 75%, rgba(23, 162, 184, 0.4) 75%, rgba(23, 162, 184, 0.4) 100%); }
    @keyframes carga-flow { from { background-position: 0 0; } to { background-position: 40px 0; } }
    #carga-wire-main { top: 50%; left: 0; width: 100%; height: 8px; margin-top: -4px; }
    #carga-wire-t-junction-cap { top: 25%; left: 75%; width: 8px; height: 25%; margin-left: -4px; }
    #carga-wire-t-junction-dut { bottom: 25%; left: 75%; width: 8px; height: 25%; margin-left: -4px; }
    .card-header-alert { background-color: var(--cor-perigo); color: white; }
    
    /* --- FIM DOS ESTILOS DO SIMULADOR --- */
    
    /* 10. Ajustes Finais e Classes Utilitárias (para manter compatibilidade) */
    .fas { margin-right: 0.75rem; }
    .no-load-risks li { margin-bottom: 8px; padding-left: 1.5em; position: relative; font-size: 14px; }
    .no-load-risks li::before { content: "⚠️"; position: absolute; left: 0; font-size: 14px; }
    .chart-container canvas { max-height: 250px; }
    .d-none { display: none; }
    .sticky { position: sticky; top: 0; z-index: 10; }
    .left-0 { left: 0; }
    .z-10 { z-index: 10; }
    .sut-tap-ideal-highlight { background-color: rgba(255, 193, 7, 0.15); }
    #slide-ecosystem-table .font-mono { font-family: 'Courier New', monospace; background: #ecf0f1; padding: 2px 5px; border-radius: 3px;}
    .text-xs { font-size: 0.875rem !important; }
    .space-y-0\.5 > * + * { margin-top: 0.5rem; }
    .space-y-px > * + * { margin-top: 1px; }
    .list-decimal, .list-disc { padding-left: 1.5rem; }
    .font-semibold { font-weight: 600; }
    .font-bold { font-weight: 700; }
    .mt-1 { margin-top: 0.25rem; }
    .mt-2 { margin-top: 0.5rem; }
    .mt-3 { margin-top: 1rem; }
    .mb-1 { margin-bottom: 0.25rem; }
    .text-lg { font-size: 1.125rem; }
    .text-sm { font-size: 0.875rem; }
    .text-center { text-align: center; }
    .italic { font-style: italic; }
    .overflow-x-auto { overflow-x: auto; }
    .min-w-full { min-width: 100%; }
    .align-middle { vertical-align: middle; }
    .switch { display: inline-flex; align-items: center; background-color: #e9ecef; border: 1px solid #ced4da; border-radius: 4px; padding: 4px 8px; margin: 2px; }
    .switch-icon { margin-right: 5px; }

    /* ESTILO ADICIONAL PARA IMAGENS DOS GRÁFICOS */
    .eps-chart-image {
        width: 100%;
        height: auto;
        border: 1px solid var(--cor-borda-suave);
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }

    /* ESTILOS PARA IMPRESSÃO (PDF) */
    @media print {
      body {
        background-color: #fff !important;
        color: #000 !important;
        font-size: 12pt;
        -webkit-print-color-adjust: exact; /* Força a impressão de cores de fundo no Chrome */
        print-color-adjust: exact;
      }

      /* Esconder elementos de navegação e interativos */
      .sidebar, #sidebar-toggle, .sidebar-search, .footer, .slide-container .footer-main .logo-main, .simulator-container .btn {
        display: none !important;
      }
      .no-print {
        display: none !important;
      }
      
      /* Ajustar layout principal */
      .main-content {
        margin-left: 0 !important;
        width: 100% !important;
        height: auto !important;
        overflow: visible !important;
        padding: 0 !important;
      }
      
      /* Ajustar cada slide para impressão */
      .slide-container {
        page-break-before: always; /* Cada slide começa em uma nova página */
        box-shadow: none !important;
        border: 1px solid #ccc !important;
        border-radius: 0 !important;
        padding: 20px !important;
        min-height: auto !important;
        margin-bottom: 0;
      }

      /* Evitar que o primeiro slide tenha uma página em branco antes dele */
      #slide-title-main {
        page-break-before: auto;
      }

      /* Garantir que tabelas e imagens se ajustem */
      table, .table-container, img, .eps-chart-image, .flowchart-svg, canvas {
        max-width: 100% !important;
        page-break-inside: avoid; /* Tenta não quebrar o elemento no meio de uma página */
      }

      .title-main, .slide-title {
        font-size: 24pt !important;
      }
      
      /* Resetar cores para economizar tinta e melhorar legibilidade */
      .highlight-text, .warning-text, .success-text, .danger-text {
        color: #000 !important;
        font-weight: bold;
        border-bottom: 2px dotted #999;
      }
      
      .formula {
        background-color: #eee !important;
        border-left-color: #999 !important;
      }
      
      th {
        background-color: #ddd !important;
        color: #000 !important;
      }
    }
  </style>
</head>

<body>
  <!-- BOTÃO PARA TOGGLE DO SIDEBAR -->
  <button id="sidebar-toggle" title="Alternar Menu">
    <i class="fas fa-bars"></i>
  </button>

  <div class="sidebar">
    <div class="sidebar-title"><i class="fas fa-cogs"></i>Planejador TTS</div>
    <div class="sidebar-search">
      <input type="text" id="searchInput" placeholder="Buscar no conteúdo...">
    </div>
    <ul>
      <li><a href="#slide-title-main" class="active"><i class="fas fa-home fa-fw"></i>Título</a></li>
      <li><a href="#slide-agenda"><i class="fas fa-calendar-alt fa-fw"></i>Agenda e Desafio</a></li>
      <div class="nav-section-title">Componentes do Sistema</div>
      <li><a href="#slide-eps-introduction"><i class="fas fa-charging-station fa-fw"></i>Introdução à Fonte (EPS)</a></li>
      <li><a href="#slide-sut-introduction"><i class="fas fa-random fa-fw"></i>Introdução ao SUT</a></li>
      <li><a href="#slide-ecosystem"><i class="fas fa-network-wired fa-fw"></i>Ecossistema de Teste</a></li>
      <li><a href="#slide-ecosystem-table"><i class="fas fa-table fa-fw"></i>Ecossistema (Tabela)</a></li>
      <div class="nav-section-title">Análise de Ensaios</div>
      <li><a href="#slide-transformer-data"><i class="fas fa-database fa-fw"></i>Dados do Transformador</a></li>
      <li><a href="#slide-applied-voltage"><i class="fas fa-bolt fa-fw"></i>Tensão Aplicada</a></li>
      <li><a href="#slide-induced-voltage-intro"><i class="fas fa-wave-square fa-fw"></i>Tensão Induzida (Intro)</a></li>
      <li><a href="#slide-induced-voltage-logic-flowchart"><i class="fas fa-project-diagram fa-fw"></i>Tensão Induzida (Lógica Fonte)</a></li>
      <li><a href="#slide-induced-voltage-analysis"><i class="fas fa-chart-line fa-fw"></i>Tensão Induzida (Análise)</a></li>
      <li><a href="#slide-no-load-losses-intro"><i class="fas fa-plug fa-fw"></i>Perdas em Vazio (Intro)</a></li>
      <li><a href="#slide-no-load-losses-risks"><i class="fas fa-exclamation-triangle fa-fw"></i>Perdas em Vazio (Riscos)</a></li>
      <li><a href="#slide-no-load-losses-analysis"><i class="fas fa-chart-line fa-fw"></i>Perdas em Vazio (Análise)</a></li>
      <li><a href="#slide-no-load-losses-analysis-table"><i class="fas fa-table fa-fw"></i>Perdas em Vazio (Tabela)</a></li>
      <li><a href="#slide-no-load-losses-simulator"><i class="fas fa-gamepad fa-fw"></i>Perdas em Vazio (Simulador)</a></li>
      <div class="nav-section-title">Perdas em Carga</div>
      <li><a href="#slide-capacitor-bank-introduction"><i class="fas fa-cubes fa-fw"></i>Introdução ao Banco de Caps</a></li>
      <li><a href="#slide-load-losses-intro"><i class="fas fa-weight-hanging fa-fw"></i>Contexto e Desafio</a></li>
      <li><a href="#slide-load-losses-bank-selection"><i class="fas fa-layer-group fa-fw"></i>Seleção Tensão Banco</a></li>
      <li><a href="#slide-load-losses-bank-structure"><i class="fas fa-cubes fa-fw"></i>Estrutura do Banco</a></li>
      <li><a href="#slide-load-losses-q-switches"><i class="fas fa-toggle-off fa-fw"></i>Chaves Q e Potência</a></li>
      <li><a href="#slide-load-losses-group-limits"><i class="fas fa-sliders-h fa-fw"></i>Limites de Grupo/Tensão</a></li>
      <li><a href="#slide-load-losses-optimization"><i class="fas fa-brain fa-fw"></i>Otimização (Chaves Q/Strings)</a></li>
      <li><a href="#slide-load-losses-cs-keys"><i class="fas fa-key fa-fw"></i>Configuração Chaves CS</a></li>
      <li><a href="#slide-load-losses-type-factor"><i class="fas fa-calculator fa-fw"></i>Fator de Tipo na I<sub>EPS</sub></a></li>
      <li><a href="#slide-load-losses-final-eps"><i class="fas fa-tachometer-alt fa-fw"></i>Análise Final I<sub>EPS</sub></a></li>
      <li><a href="#slide-load-losses-results-table"><i class="fas fa-table fa-fw"></i>Tabela de Resultados</a></li>
      <li><a href="#slide-load-losses-sut-eps-subtable"><i class="fas fa-search-dollar fa-fw"></i>Sub-Tabela SUT/EPS</a></li>
      <li><a href="#slide-load-losses-simulator"><i class="fas fa-gamepad fa-fw"></i>Perdas em Carga (Simulador)</a></li>
      <div class="nav-section-title">Finalização</div>
      <li><a href="#slide-conclusion"><i class="fas fa-flag-checkered fa-fw"></i>Conclusão</a></li>
      <li><a href="#slide-qa"><i class="fas fa-question-circle fa-fw"></i>Perguntas</a></li>
    </ul>
  </div>

  <div class="main-content" id="main-content-area">
    <!-- Slide Título -->
    <div class="slide-container" id="slide-title-main" style="align-items: center; justify-content: center;">
      <div class="title-main">Plataforma Inteligente de Planejamento de Ensaios em Transformadores</div>
      <div class="subtitle-main">Maximizando a Eficiência e Segurança através da Análise Preditiva de Limites do LEAT</div>
      <div class="highlight-box-main"><p class="text-lg" style="color: var(--cor-texto-principal);">Da Teoria à Otimização Prática</p></div>
      <div class="footer-main">
        <div class="logo-main"><i class="fas fa-industry text-5xl" style="color: var(--cor-primaria);"></i></div>
        <div class="date-main">Junho 2025</div>
      </div>
    </div>

    <!-- Slide Agenda e Desafio Central -->
    <div class="slide-container" id="slide-agenda">
        <div class="slide-title">Agenda e O Desafio Central</div>
        <div class="content-container">
            <div class="left-section"><div class="section-title-box">Agenda da Apresentação</div><div class="info-box">
                <div class="agenda-item"><div class="agenda-icon"><i class="fas fa-rocket fa-fw"></i></div><div>Introdução: O Ecossistema de Ensaios e Seus Limites</div></div>
                <div class="agenda-item"><div class="agenda-icon"><i class="fas fa-file-invoice-dollar fa-fw"></i></div><div>Fundação: Dados do Transformador e Cálculos Derivados</div></div>
                <div class="agenda-item"><div class="agenda-icon"><i class="fas fa-bolt fa-fw"></i></div><div>Análise de Ensaios Dielétricos (Aplicada e Induzida)</div></div>
                <div class="agenda-item"><div class="agenda-icon"><i class="fas fa-tachometer-alt fa-fw"></i></div><div>Análise de Perdas em Vazio: Gerenciando a Potência do EPS</div></div>
                <div class="agenda-item"><div class="agenda-icon"><i class="fas fa-battery-three-quarters fa-fw"></i></div><div>Deep Dive: Análise de Perdas em Carga e a Otimização do Banco de Capacitores</div></div>
                <div class="agenda-item"><div class="agenda-icon"><i class="fas fa-trophy fa-fw"></i></div><div>Conclusão: Valor Agregado da Plataforma</div></div>
            </div></div>
            <div class="right-section"><div class="section-title-box">O Desafio Central da Plataforma</div><div class="info-box">
                <div class="challenge-content">
                <p>Ensaios de alta potência não se resumem a aplicar tensão e corrente.</p>
                <p>Trata-se de <span class="highlight-text">orquestrar um ecossistema complexo de equipamentos</span>, cada um com limites operacionais e de segurança.</p>
                <p class="mt-3">A plataforma foi desenvolvida para responder à pergunta fundamental:</p>
                <p class="text-center text-base font-semibold mt-3 p-3 rounded-md">"Com base nos dados do transformador e nos limites dos nossos equipamentos (EPS, SUT, Banco, Sist. Ressonante), é <span class="success-text">possível</span> e <span class="danger-text">seguro</span> realizar o ensaio, e qual a <span class="warning-text">configuração ótima</span>?"</p>
                </div></div></div>
        </div>
        <div class="footer">Plataforma Inteligente de Planejamento de Ensaios em Transformadores</div>
    </div>

    <!-- NOVO SLIDE: Introdução à Fonte (EPS) -->
    <div class="slide-container" id="slide-eps-introduction">
        <div class="slide-title">O Coração do Sistema: A Fonte de Potência (EPS 1500)</div>
        <div class="content-container">
            <div class="left-section">
                <div class="info-box">
                    <div class="section-title-box">O que é o EPS?</div>
                    <p>O <strong>EPS (Electronic Power Supply)</strong>, ou Conversor de Frequência Estático (SFC), é o componente principal do campo de teste. Ele substitui os antigos motogeradores, fornecendo uma fonte de alimentação AC <span class="highlight-text">compacta, limpa, silenciosa e altamente controlável</span>.</p>
                </div>
                <div class="info-box">
                    <div class="section-title-box">Principais Vantagens e Capacidades</div>
                    <ul class="list-disc pl-4 text-sm space-y-1">
                        <li><strong>Frequência Variável:</strong> Permite testes de 50/60 Hz e tensão induzida (16 a 300 Hz) sem reconexão.</li>
                        <li><strong>Qualidade de Energia:</strong> Controle online de simetria de fase, supressão de DC para evitar saturação do núcleo, e controle de THD (distorção harmônica) para atender às normas IEC/ANSI.</li>
                        <li><strong>Desacoplamento da Rede:</strong> A estabilidade do ensaio não depende mais da rede elétrica externa.</li>
                        <li><strong>Flexibilidade de Instalação:</strong> Sem requisitos especiais de construção civil, fundação ou controle de ruído.</li>
                    </ul>
                </div>
            </div>
            <div class="right-section">
                <div class="info-box">
                    <div class="section-title-box">Especificações Chave (Sistema EPS 1500)</div>
                    <div class="overflow-x-auto text-xs">
                        <table>
                            <tbody>
                                <tr><td><strong>Potência Aparente Total</strong></td><td class="font-mono">~1500 kVA</td></tr>
                                <tr><td><strong>Tensão de Saída Nominal</strong></td><td class="font-mono">480 V AC</td></tr>
                                <tr><td><strong>Corrente de Saída (por unidade)</strong></td><td class="font-mono">650 A</td></tr>
                                <tr><td><strong>Faixa de Frequência Operacional</strong></td><td class="font-mono">16 Hz - 300 Hz</td></tr>
                                <tr><td><strong>Distorção Harmônica (THD)</strong></td><td class="font-mono">≤ 5%</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="info-box">
                    <div class="section-title-box">Curvas de Performance (Potência vs Ângulo de Carga)</div>
                    <div class="row g-2">
                        <div class="col-4"><img src="assets/eps_high_power_mode.png" alt="Gráfico High Power Mode" class="eps-chart-image"></div>
                        <div class="col-4"><img src="assets/eps_medium_power_mode.png" alt="Gráfico Medium Power Mode" class="eps-chart-image"></div>
                        <div class="col-4"><img src="assets/eps_low_power_mode.png" alt="Gráfico Low Power Mode" class="eps-chart-image"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer">O EPS é a base para a realização de ensaios de perdas, elevação de temperatura e tensão induzida.</div>
    </div>
    
    <!-- NOVO SLIDE: Introdução ao SUT -->
    <div class="slide-container" id="slide-sut-introduction">
        <div class="slide-title">O Intermediário Essencial: SUT (Set-Up Transformer)</div>
        <div class="content-container">
            <div class="left-section">
                <div class="info-box">
                    <div class="section-title-box">Função Principal do SUT</div>
                    <p>O <strong>SUT (Set-Up Transformer)</strong> é um transformador elevador que atua como a ponte entre a fonte de baixa tensão (EPS, 480V) e o transformador de alta tensão a ser testado (DUT).</p>
                    <p>Sua função é <span class="highlight-text">elevar a tensão do EPS para o nível exigido pelo ensaio</span>, que pode chegar a centenas de quilovolts.</p>
                </div>
                <div class="info-box">
                    <div class="section-title-box">Características e Limites</div>
                    <ul class="list-disc pl-4 text-sm space-y-1">
                        <li><strong>Entrada (BT):</strong> Nominalmente alimentado com <span class="font-mono">480 V</span> pelo EPS.</li>
                        <li><strong>Saída (AT):</strong> Possui um <span class="warning-text">amplo range de tensão de saída</span>, tipicamente de <span class="font-mono">14 kV a 140 kV</span>.</li>
                        <li><strong>Controle por Taps:</strong> A seleção da tensão de saída é feita através de múltiplos taps, permitindo um ajuste fino para cada cenário de ensaio.</li>
                    </ul>
                </div>
            </div>
            <div class="right-section">
                <div class="info-box">
                    <div class="section-title-box">O Desafio do SUT e a Inteligência da Plataforma</div>
                    <p>O principal desafio é garantir que a tensão de saída necessária para o ensaio no DUT <span class="danger-text">não exceda os limites operacionais do próprio SUT</span> (14kV - 140kV).</p>
                    <div class="key-point mt-2">
                        <strong class="warning-text">Inteligência da Plataforma:</strong> A plataforma calcula a tensão exata necessária na saída do SUT e <span class="highlight-text">seleciona o tap ideal</span> que atenda a essa demanda, respeitando rigorosamente seus limites. Se nenhum tap for viável, o ensaio é marcado como CRÍTICO, prevenindo danos ao equipamento.
                    </div>
                </div>
            </div>
        </div>
        <div class="footer">A seleção correta do tap do SUT é um dos pilares da análise de viabilidade dos ensaios.</div>
    </div>
    
    <!-- Slide Ecossistema de Teste -->
    <div class="slide-container" id="slide-ecosystem">
        <div class="slide-title">O Ecossistema de Teste e Seus Limites Críticos</div>
        <div class="content-section">
             <svg class="flowchart-svg" width="100%" height="280" viewBox="0 0 950 280">
                <defs><marker id="arrowhead-eco" markerWidth="7" markerHeight="5" refX="6" refY="2.5" orient="auto"><polygon points="0 0, 7 2.5, 0 5" fill="var(--cor-primaria)"/></marker></defs>
                <!-- Nodes -->
                <g class="node" transform="translate(50, 100)"><rect width="130" height="70"/><text x="65" y="30" font-size="13">DUT</text><text x="65" y="50" font-size="9">(Transformador)</text></g>
                <g class="node" transform="translate(250, 100)"><rect width="130" height="70"/><text x="65" y="30" font-size="13">SUT</text><text x="65" y="50" font-size="9">(Transf. Elevador)</text></g>
                <g class="node end" transform="translate(450, 100)"><rect width="130" height="70"/><text x="65" y="30" font-size="13">EPS</text><text x="65" y="50" font-size="9">(Fonte de Potência)</text></g>
                <g class="node action" transform="translate(650, 180)"><rect width="130" height="55"/><text x="65" y="22" font-size="11">Banco Capacitores</text><text x="65" y="38" font-size="9">(Perdas em Carga)</text></g>
                <g class="node decision" transform="translate(50, 190)"><rect width="130" height="55"/><text x="65" y="22" font-size="10">Sistema Ressonante</text><text x="65" y="38" font-size="9">(Tensão Aplicada)</text></g>
                <!-- Edges -->
                <g class="edge"><path d="M180 135 H250" marker-end="url(#arrowhead-eco)"/><text x="215" y="125" font-size="9">Alimentação DUT</text></g>
                <g class="edge"><path d="M380 135 H450" marker-end="url(#arrowhead-eco)"/><text x="415" y="125" font-size="9">Alimentação SUT</text></g>
                <line x1="315" y1="170" x2="315" y2="205" stroke="#f39c12" stroke-width="1.5" stroke-dasharray="4,2"/>
                <line x1="315" y1="205" x2="650" y2="207" stroke="#f39c12" stroke-width="1.5" stroke-dasharray="4,2" marker-end="url(#arrowhead-eco)"/>
                <text x="480" y="195" fill="#e67e22" font-size="9">Compensação Reativa (P. Carga)</text>
                <g class="edge"><path d="M115 170 V189" stroke-dasharray="4,2" marker-end="url(#arrowhead-eco)"/></g>
            </svg>
            <p class="text-center mt-1 text-sm">O fluxo de potência parte do <strong class="highlight-text">EPS</strong>, que alimenta o <strong class="highlight-text">SUT (Set-Up Transformer)</strong>, responsável por elevar a tensão ao nível necessário para o <strong class="highlight-text">DUT (Device Under Test)</strong>. Para ensaios específicos, equipamentos auxiliares são cruciais: o <span class="warning-text">Banco de Capacitores</span> compensa a potência reativa em ensaios de perdas em carga, e o <span class="success-text">Sistema Ressonante</span> é usado para o ensaio de tensão aplicada.</p>
        </div>
        <div class="footer">Valores de `constants.py` são a base da análise de limites.</div>
    </div>

    <!-- Slide Tabela Ecossistema -->
    <div class="slide-container" id="slide-ecosystem-table">
        <div class="slide-title">Tabela: Parâmetros de Limite do Ecossistema</div>
        <div class="table-container">
            <table>
                <thead><tr><th>Componente</th><th>Parâmetro Chave</th><th>Valor de Referência (`constants.py`)</th></tr></thead>
                <tbody>
                    <tr><td>Fonte (EPS)</td><td>Potência Aparente do Sistema</td><td class="font-mono danger-text">EPS_APARENTE_POWER (1450.0 kVA)</td></tr>
                    <tr><td>Fonte (EPS)</td><td>Corrente Limite Positiva</td><td class="font-mono danger-text">EPS_CURRENT_LIMIT_POSITIVE (2000.0 A)</td></tr>
                    <tr><td>Fonte (EPS)</td><td>Corrente Limite Negativa</td><td class="font-mono danger-text">EPS_CURRENT_LIMIT_NEGATIVE (-2000.0 A)</td></tr>
                    <tr><td>Transf. Elevador (SUT)</td><td>Tensão BT Nominal</td><td class="font-mono">SUT_BT_VOLTAGE (480.0 V)</td></tr>
                    <tr><td>Transf. Elevador (SUT)</td><td>Range Tensão AT</td><td class="font-mono">14kV - 140kV (SUT_AT_MIN/MAX_VOLTAGE)</td></tr>
                    <tr><td>Banco de Capacitores</td><td>Função Primária</td><td class="font-mono info-text">Compensação de Potência Reativa (MVAr)</td></tr>
                    <tr><td>Carga (DUT)</td><td>Potência Ativa Limite</td><td class="font-mono warning-text">DUT_POWER_LIMIT_TRIFASICO(1350.0 kW)</td></tr>
                </tbody>
            </table>
        </div>
        <div class="footer">Valores de `constants.py` e documentos do fabricante são a base da análise.</div>
    </div>

    <!-- Slide Dados do Transformador -->
    <div class="slide-container" id="slide-transformer-data">
        <div class="slide-title">Fundação: Dados do Transformador e Cálculos Derivados</div>
        <div class="content-container">
            <div class="left-section">
                <div class="info-box">
                    <div class="section-title-box">Inputs Essenciais do Usuário</div>
                    <div class="info-box text-xs">
                        <ul class="list-disc pl-4 space-y-0.5">
                            <li><span class="font-semibold">Gerais:</span> Potência (MVA), Frequência (Hz), Tipo (Tri/Mono), Grupo Ligação, Líquido Isolante, Norma (IEC/IEEE).</li>
                            <li><span class="font-semibold">Enrolamentos (AT, BT, Terciário):</span> Tensão Nominal (kV), Classe de Tensão (kV), Conexão (Y, D, Z, N), Dados de Neutro (Classe, NBI, SIL).</li>
                            <li><span class="font-semibold">Impedâncias:</span> Nominal (%), Taps Maior/Menor (%).</li>
                            <li><span class="font-semibold">Níveis de Isolamento Linha/Neutro:</span> NBI (kVp), SIL/BSL (kVp), Tensão Aplicada (kVrms), Tensão Induzida (kVrms).</li>
                             <li><span class="font-semibold">Tensões de Ensaio:</span> Aplicada e Induzida para cada enrolamento e neutro.</li>
                        </ul>
                        <p class="mt-1 text-xs italic">Interface: `transformer_inputs.html`, Lógica de Dropdowns: `insulation_levels.js`</p>
                    </div>
                     <div class="info-box">
                        <div class="section-title-box">Importância da Precisão dos Dados</div>
                        <p class="text-xs">A exatidão dos dados de entrada é <span class="highlight-text">fundamental</span>. Erros aqui se propagam:</p>
                        <ul class="list-disc pl-4 mt-1 space-y-px text-xs">
                            <li class="danger-text">Planejamento de ensaios inviáveis ou arriscados.</li>
                            <li class="warning-text">Seleção incorreta de equipamentos e configurações.</li>
                            <li>Resultados de simulação e análise de limites falhos.</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="right-section">
                <div class="info-box">
                    <div class="section-title-box">Cálculos Derivados Automaticamente (`transformer_service.py`)</div>
                    <div class="formula-box text-xs">
                        <div class="formula">I<sub>nom_A</sub> = (S<sub>MVA</sub> × 10<sup>3</sup>) / (Fator × V<sub>nom_kV</sub>)</div>
                        <div class="formula-description">Correntes Nominais (A). Fator = √3 (trifásico) ou 1.0 (monofásico).</div>

                        <div class="formula">Z<sub>base_AT_Ω</sub> = (V<sub>AT_kV</sub>² × 10<sup>3</sup>) / S<sub>MVA</sub></div>
                        <div class="formula-description">Impedância Base AT (Ω).</div>

                        <div class="formula">Z<sub>cc_Ω</sub> = Z<sub>base_AT_Ω</sub> × (Z<sub>cc_%</sub> / 100)</div>
                        <div class="formula-description">Impedância de Curto-Circuito (Ω).</div>

                        <div class="formula">L<sub>cc_H</sub> = Z<sub>cc_Ω</sub> / (2π × f<sub>Hz</sub>) ⇒ L<sub>cc_µH</sub></div>
                        <div class="formula-description">Indutância de Curto-Circuito (H e µH).</div>

                        <div class="formula">IAC<sub>kVp</sub> = NBI<sub>kVp</sub> × 1.1 (se IEC)</div>
                        <div class="formula-description">Impulso Atmosférico Cortado (kVp). (`constants.IAC_NBI_FACTOR`)</div>
                        <div class="formula">Impedância Tap<sub>x</sub> = Z<sub>nom_%</sub> × (V<sub>tap_x_kV</sub> / V<sub>nom_kV</sub>)²</div>
                        <div class="formula-description">Impedância nos taps (se não fornecida).</div>
                    </div>
                    <div class="info-box text-xs">
                        <div class="section-title-box">Painel de Informações Consolidado</div>
                        <p>No topo de cada módulo de ensaio, um painel exibe dinamicamente todos estes dados (entradas e calculados), garantindo referência consistente. Atualizado via `common_module.js`.</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer">A base sólida de dados garante a confiabilidade de toda a plataforma.</div>
    </div>

    <!-- Slide Tensão Aplicada -->
    <div class="slide-container" id="slide-applied-voltage">
        <div class="slide-title">Ensaio de Tensão Aplicada - Viabilidade com Sistema Ressonante</div>
        <div class="content-container">
            <div class="left-section">
                <div class="info-box">
                    <div class="section-title-box">Objetivo e Desafio</div>
                    <p>Verificar isolamento entre enrolamentos e terra com tensão AC em frequência industrial ou elevada.</p>
                    <div class="key-point mt-2"><strong class="warning-text">Ponto Chave:</strong> Para tensões elevadas, o Sistema Ressonante High Volt é frequentemente necessário. A plataforma analisa sua viabilidade.</div>
                </div>
                <div class="logic-steps">
                    <div class="section-title-box">Lógica de Decisão (applied_voltage_service.py)</div>
                    <ol class="list-decimal ml-5 space-y-1 text-sm">
                        <li><strong>Carga Capacitiva Total (C<sub>ensaio</sub>):</strong><br>
                            C<sub>ensaio</sub> = C<sub>trafo_manual</sub> + C<sub>divisor_tensão_auto</sub>
                            <ul class="list-disc ml-5 text-xs">
                                <li>C<sub>divisor</sub> = 330 pF (se V<sub>teste</sub> > 450kV)</li>
                                <li>C<sub>divisor</sub> = 660 pF (se V<sub>teste</sub> ≤ 450kV)</li>
                            </ul>
                        </li>
                        <li><strong>Prioridade:</strong> Verifica se a config. "Módulos 1||2||3 (3 Par.)" (450kV ou 270kV) atende V<sub>teste</sub> e C<sub>ensaio</sub> (convertida para nF).</li>
                        <li><strong>Alternativas:</strong> Se não, busca em outras configs. (`RESONANT_SYSTEM_CONFIGS`) ordenadas.</li>
                        <li><strong>Resultado:</strong> Indica configuração viável ou motivo da inviabilidade.</li>
                    </ol>
                </div>
            </div>
            <div class="right-section">
                <div class="info-box">
                    <div class="section-title-box">Configurações Típicas do Sistema Ressonante</div>
                    <div class="overflow-x-auto text-xs">
                        <table>
                            <thead><tr><th>Configuração (High Volt WRM)</th><th>V<sub>máx</sub> (kV)</th><th>C<sub>min</sub> (nF)</th><th>C<sub>máx</sub> (nF)</th></tr></thead>
                            <tbody>
                                <tr><td>Módulos 1+2+3 (Série)</td><td>1350</td><td>0.22</td><td>2.6</td></tr>
                                <tr class="sut-tap-ideal-highlight"><td class="font-semibold">Módulos 1||2||3 (3 Par.) 450kV</td><td class="font-semibold">450</td><td class="font-semibold">2.0</td><td class="font-semibold">23.6</td></tr>
                                <tr class="sut-tap-ideal-highlight"><td class="font-semibold">Módulos 1||2||3 (3 Par.) 270kV</td><td class="font-semibold">270</td><td class="font-semibold">2.0</td><td class="font-semibold">39.3</td></tr>
                                 <tr><td>Módulos 1+2 (Série)</td><td>900</td><td>0.3</td><td>6.5</td></tr>
                                <tr><td>Módulo 1 (1 em Par.)</td><td>450</td><td>0.7</td><td>13.1</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <p class="mt-2 text-xs italic">Ref: `constants.RESONANT_SYSTEM_CONFIGS`</p>
                </div>
                <div class="result-box">
                    <div class="section-title-box">Exemplos de Saída da Análise</div>
                    <div class="text-xs space-y-1">
                        <p><span class="success-text"><i class="fas fa-check-circle"></i> Ideal:</span> Módulos 1||2||3 (3 Par.) 450kV. Limites: 450kV / 2.0-23.6nF. (Nota: Capacitância de X.XX nF inclui Y.YY nF do divisor).</p>
                        <p><span class="warning-text"><i class="fas fa-exclamation-triangle"></i> Alternativa:</span> Módulo 1 (1 em Par.). Limites: 450kV / 0.7-13.1nF. (Nota: ...)</p>
                        <p><span class="danger-text"><i class="fas fa-times-circle"></i> Não Viável:</span> Inválido: Capacitância (0.50 nF) abaixo do mínimo (0.70 nF) para esta tensão. (Nota: ...)</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer">Interface: `applied_voltage.html` e `applied_voltage.js`. Lógica: `applied_voltage_service.py`.</div>
    </div>

    <!-- Slide Tensão Induzida - Introdução -->
    <div class="slide-container" id="slide-induced-voltage-intro">
        <div class="slide-title">Ensaio de Tensão Induzida - Introdução e Desafio da Fonte</div>
        <div class="content-container">
            <div class="left-section">
                <div class="info-box">
                    <div class="section-title-box">Objetivo e Desafio Principal</div>
                    <p>Verificar o isolamento entre espiras de um mesmo enrolamento, alimentando o transformador em frequência elevada (geralmente 2 a 3 vezes a nominal). O desafio é selecionar a fonte de alimentação (lado BT ou Terciário do SUT) para não exceder os limites operacionais do SUT.</p>
                    <div class="key-point mt-2"><strong class="warning-text">Ponto Chave:</strong> A tensão no lado de alta tensão do SUT deve permanecer dentro do seu range operacional (14-140kV), o que dita a necessidade de usar o lado BT ou o Terciário como fonte primária de alimentação.</div>
                </div>
            </div>
            <div class="right-section">
                <div class="info-box">
                    <div class="section-title-box">A Importância da Lógica de Seleção da Fonte</div>
                    <p>A plataforma implementa uma lógica inteligente para determinar a fonte mais adequada, considerando os limites de tensão do SUT e a disponibilidade de enrolamentos (BT e Terciário).</p>
                    <p class="mt-2">A decisão impacta diretamente:</p>
                    <ul class="list-disc text-sm">
                        <li>A viabilidade do ensaio.</li>
                        <li>A corrente demandada do EPS.</li>
                        <li>A configuração e os limites do sistema.</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer">A correta seleção da fonte é o primeiro passo para um ensaio seguro.</div>
    </div>

    <!-- Slide Tensão Induzida - Lógica de Seleção da Fonte -->
    <div class="slide-container" id="slide-induced-voltage-logic-flowchart">
        <div class="slide-title">Lógica de Seleção de Fonte (`check_if_need_tertiary_for_induced_voltage`)</div>
        <div class="flowchart-container" style="height:600px;">
            <svg class="flowchart-svg" width="100%" height="100%" viewBox="0 0 600 700" style="max-width:100%; max-height:100%;">
                <defs>
                    <marker id="arrowhead-induced" markerWidth="6" markerHeight="4" refX="5.5" refY="2" orient="auto">
                        <polygon points="0 0, 6 2, 0 4" fill="var(--cor-texto-secundario)" />
                    </marker>
                </defs>

                <!-- Start Node -->
                <g class="node" transform="translate(235, 30)">
                    <rect width="130" height="40" />
                    <text x="65" y="20">INÍCIO</text>
                </g>

                <!-- Decision 1: Check BT Voltage Range -->
                <g class="node decision" transform="translate(180, 120)">
                    <rect width="240" height="65" />
                    <text x="120" y="25" font-size="11">V<tspan baseline-shift="sub" font-size="9">BT_aplicada_inicial_kV</tspan></text>
                    <text x="120" y="45" font-size="11">dentro Limites SUT AT (14-140kV)?</text>
                </g>

                <!-- Action: Use BT Source -->
                <g class="node action" transform="translate(50, 250)">
                    <rect width="150" height="50" />
                    <text x="75" y="25">Fonte = BT</text>
                </g>

                <!-- Decision 2: Check if Tertiary is Available -->
                <g class="node decision" transform="translate(380, 250)">
                    <rect width="150" height="50" />
                    <text x="75" y="20" font-size="12">Terciário</text>
                    <text x="75" y="35" font-size="12">Disponível?</text>
                </g>

                <!-- Action: Use Tertiary Source -->
                <g class="node action" transform="translate(280, 430)">
                    <rect width="150" height="50" />
                    <text x="75" y="25">Fonte = Terciário</text>
                </g>

                <!-- End Node: Test Critical -->
                <g class="node end" transform="translate(450, 430)">
                    <rect width="150" height="50" />
                    <text x="75" y="25">TESTE CRÍTICO</text>
                </g>

                <!-- End Node: Proceed (BT) -->
                <g class="node end" transform="translate(50, 380)">
                    <rect width="150" height="40" />
                    <text x="75" y="20" class="success-text">PROCEDER (BT)</text>
                </g>

                <!-- End Node: Proceed (Tertiary) -->
                <g class="node end" transform="translate(280, 560)">
                    <rect width="150" height="40" />
                    <text x="75" y="20" class="success-text">PROCEDER (Terciário)</text>
                </g>

                <!-- Edges -->
                <g class="edge"><path d="M300 70 L300 120" marker-end="url(#arrowhead-induced)" /></g>
                <g class="edge"><path d="M300 185 L125 250" marker-end="url(#arrowhead-induced)" /><text x="210" y="210" font-size="10">Sim</text></g>
                <g class="edge"><path d="M125 300 L125 380" marker-end="url(#arrowhead-induced)" /></g>
                <g class="edge"><path d="M420 185 L455 250" marker-end="url(#arrowhead-induced)" /><text x="410" y="210" font-size="10">Não</text></g>
                <g class="edge"><path d="M455 300 L355 430" marker-end="url(#arrowhead-induced)" /><text x="430" y="360" font-size="10">Sim</text></g>
                <g class="edge"><path d="M455 300 L525 430" marker-end="url(#arrowhead-induced)" /><text x="500" y="360" font-size="10">Não</text></g>
                <g class="edge"><path d="M355 480 L355 560" marker-end="url(#arrowhead-induced)" /></g>
            </svg>
        </div>
        <div class="footer">A lógica determina a fonte primária de alimentação para o ensaio.</div>
    </div>

    <!-- Slide Tensão Induzida - Análise -->
    <div class="slide-container" id="slide-induced-voltage-analysis">
        <div class="slide-title">Análise de Limites do SUT e EPS</div>
        <div class="content-container">
            <div class="left-section">
                <div class="info-box">
                    <div class="section-title-box">Análise de Limites do SUT</div>
                    <p class="text-sm">Após a seleção da fonte (BT ou Terciário), a plataforma verifica se a tensão de ensaio necessária se encaixa no range operacional do SUT (14-140kV).</p>
                    <p class="text-sm mt-2">Se a V<sub>teste</sub> for fora deste range, o ensaio torna-se inviável com a configuração atual.</p>
                </div>
                <div class="formula-box">
                    <div class="section-title-box">Cálculo da Tensão de Ensaio (V<sub>BT_aplicada_inicial</sub>)</div>
                    <div class="formula">V<sub>BT_aplicada_inicial</sub> = V<sub>teste_DUT_AT</sub> / Ratio<sub>SUT</sub></div>
                    <div class="formula-description">Onde Ratio é a relação de transformação do SUT no tap selecionado.</div>
                </div>
            </div>
            <div class="right-section">
                <div class="info-box">
                    <div class="section-title-box">Análise de Corrente e Potência no EPS</div>
                    <p class="text-sm">A corrente de excitação do DUT (I<sub>exc</sub>) é vista no lado do EPS através do SUT. A plataforma calcula essa corrente:</p>
                    <div class="formula-box mt-2">
                        <div class="formula">I<sub>exc_DUT_Fonte</sub> = I<sub>DUT_exc_A</sub> × (V<sub>SUT_tap_AT_V</sub> / V<sub>SUT_BT_nom_V</sub>) × Fator<sub>Tipo_EPS</sub></div>
                        <div class="formula">I<sub>EPS_A</sub> = I<sub>exc_DUT_Fonte</sub> × (V<sub>SUT_tap_AT_V</sub> / V<sub>SUT_BT_nom_V</sub>)</div>
                        <div class="formula-description">Fator<sub>Tipo_EPS</sub> é 1.0 para trifásico e √3 para monofásico.</div>
                    </div>
                    <p class="text-sm mt-2">A corrente calculada (I<sub>EPS_A</sub>) é comparada com os limites do EPS:</p>
                    <ul class="list-disc pl-4 text-xs">
                        <li>`EPS_CURRENT_LIMIT_POSITIVE` (+2000 A)</li>
                        <li>`EPS_CURRENT_LIMIT_NEGATIVE` (-2000 A)</li>
                    </ul>
                    <p class="text-sm mt-1">Verifica-se também a potência aparente total demandada do EPS em relação a `EPS_APARENTE_POWER` (1450 kVA).</p>
                </div>
            </div>
        </div>
        <div class="footer">A análise integrada garante que o ensaio não sobrecarregue o EPS.</div>
    </div>

    <!-- Slide Perdas em Vazio - Introdução -->
    <div class="slide-container" id="slide-no-load-losses-intro">
        <div class="slide-title">Perdas em Vazio: O Que Medimos e Por Quê</div>
        <div class="single-column-section">
            <div class="info-box">
                <div class="section-title-box">Objetivo e Contexto</div>
                <p>Medir perdas no núcleo e a corrente de excitação (I<sub>exc</sub>). O transformador em vazio consome uma corrente de magnetização, que é baixa (1-5% da nominal) mas <span class="highlight-text">altamente indutiva</span>.</p>
            </div>
            <div class="info-box">
                <div class="section-title-box">Natureza da Corrente em Vazio</div>
                <p>Essa corrente é responsável por estabelecer e manter o fluxo magnético no núcleo. A maior parcela dela é <span class="warning-text">reativa (magnetizante)</span>, com um fator de potência muito baixo (tipicamente 0,01–0,3).</p>
            </div>
            <div class="info-box">
                <div class="section-title-box">Componentes das Perdas em Vazio</div>
                <p>As perdas medidas neste ensaio (potência ativa) correspondem às perdas no ferro (histerese e correntes parasitas no núcleo), sob tensão nominal. Elas representam uma fração pequena da potência nominal (<1%).</p>
                <p class="mt-2">A plataforma considera os fatores de construção e o tipo de aço para refinar os cálculos.</p>
            </div>
        </div>
        <div class="footer">Entender a natureza da carga em vazio é chave para a segurança do ensaio.</div>
    </div>

    <!-- Slide Perdas em Vazio - Riscos dos Capacitores -->
    <div class="slide-container" id="slide-no-load-losses-risks">
        <div class="slide-title">Perdas em Vazio: Riscos Críticos da Conexão de Capacitores</div>
        <div class="content-container">
            <div class="left-section">
                <div class="info-box" style="border-left: 5px solid var(--cor-perigo);">
                    <div class="section-title-box">
                        <i class="fas fa-ban mr-2" style="color: var(--cor-perigo);"></i>
                        <span class="danger-text">NÃO UTILIZAR BANCO DE CAPACITORES!</span>
                    </div>
                    <p>A prática de conectar bancos de capacitores em paralelo com o transformador durante o ensaio em vazio é <span class="danger-text">PROIBIDA E EXTREMAMENTE PERIGOSA</span>.</p>
                    <div class="key-point mt-2"><strong class="warning-text">Motivo Principal:</strong> A combinação de um indutor não-linear com um capacitor forma um circuito ressonante, propenso à <span class="danger-text">Ferro-ressonância</span>.</div>
                </div>
                <div class="info-box">
                    <div class="section-title-box">O Fenômeno da Ferro-ressonância</div>
                    <p>É uma ressonância não linear que pode levar a:</p>
                    <ul class="no-load-risks">
                        <li><span class="danger-text">Sobretensões extremas:</span> Múltiplos da tensão nominal.</li>
                        <li><span class="warning-text">Oscilações caóticas:</span> Tensão e corrente imprevisíveis.</li>
                        <li><span class="highlight-text">Danos ao Equipamento:</span> Destruição do transformador e da aparelhagem.</li>
                    </ul>
                </div>
            </div>
            <div class="right-section">
                <div class="info-box">
                    <div class="section-title-box">Riscos Adicionais e Não Conformidade</div>
                    <ul class="no-load-risks">
                        <li><strong>Distorção da Forma de Onda:</strong> Invalida as medições de perdas.</li>
                        <li><strong>Mascaramento dos Resultados:</strong> Impede a medição das características intrínsecas.</li>
                        <li><strong>Não Conformidade Normativa (IEC/IEEE):</strong> Exige excitação direta.</li>
                        <li><strong>Segurança Geral:</strong> Risco para a equipe e equipamentos.</li>
                    </ul>
                </div>
                <div class="result-box">
                    <div class="section-title-box text-center">Exemplo de Sobretensões (p.u.)</div>
                    <table class="text-xs text-center">
                        <thead><tr><th>Cenário</th><th>Tensão no Transformador</th></tr></thead>
                        <tbody>
                            <tr><td>Operação normal (sem capacitores)</td><td>~1.0 p.u.</td></tr>
                            <tr><td class="warning-text">Ferro-ressonância moderada</td><td class="warning-text">~3.0 p.u.</td></tr>
                            <tr><td class="danger-text">Ferro-ressonância severa (caos)</td><td class="danger-text">>10.0 p.u.</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="footer">A segurança e a integridade dos resultados são inegociáveis.</div>
    </div>

    <!-- Slide Perdas em Vazio - Análise Plataforma -->
    <div class="slide-container" id="slide-no-load-losses-analysis">
        <div class="slide-title">Perdas em Vazio: Abordagem Segura e Conformidade na Plataforma</div>
        <div class="single-column-section">
            <div class="info-box">
                <div class="section-title-box">Procedimento Correto</div>
                <p>A plataforma planeja o ensaio em vazio <span class="highlight-text">sem o uso de bancos de capacitores</span>, garantindo a segurança e a conformidade normativa.</p>
            </div>
            <div class="info-box">
                <div class="section-title-box">Validação dos Limites do Sistema (EPS e SUT)</div>
                <p>A análise foca em assegurar que a fonte de alimentação (EPS) e o transformador elevador (SUT) possam fornecer os requisitos do ensaio:</p>
                <ol class="list-decimal ml-5 space-y-1 mt-2">
                    <li><strong>Potência de Ensaio:</strong> Estimativa da potência aparente (S<sub>ensaio</sub>) e ativa (perdas no núcleo) que o transformador sob teste demandará.</li>
                    <li><strong>Seleção da Fonte:</strong> Identifica a fonte apropriada (BT ou Terciário) através do SUT, garantindo que a tensão aplicada ao transformador sob teste não exceda os limites operacionais do SUT (14-140kV).</li>
                    <li><strong>Análise de Limites do EPS:</strong>
                        <ul class="list-disc ml-4">
                            <li>Compara a potência aparente de ensaio com a capacidade do EPS.</li>
                            <li>Verifica a corrente de excitação refletida no EPS contra os limites de corrente.</li>
                        </ul>
                    </li>
                </ol>
<div class="info-box mt-3">
                    <div class="section-title-box">Análise Detalhada dos Limites de Corrente do EPS</div>
                    <p class="text-sm">Além da potência, a corrente demandada do EPS é um fator crítico. O sistema calcula a corrente de excitação do transformador (I<sub>exc</sub>) e a projeta para o lado do EPS, considerando a relação de transformação do SUT.</p>
                    <div class="formula-box mt-2">
                        <div class="formula">I<sub>EPS_A</sub> = I<sub>exc_DUT_Fonte</sub> × (V<sub>SUT_tap_AT_V</sub> / V<sub>SUT_BT_nom_V</sub>)</div>
                        <div class="formula-description">Onde I<sub>exc_DUT_Fonte</sub> é a corrente de excitação do DUT referida ao lado da fonte do SUT.</div>
                    </div>
                    <p class="text-sm mt-2">Esta corrente calculada (I<sub>EPS_A</sub>) é então comparada com os limites de corrente do EPS, que são:</p>
                    <ul class="list-disc pl-4 text-xs">
                        <li><span class="success-text">Limite Positivo:</span> `EPS_CURRENT_LIMIT_POSITIVE` (+2000 A)</li>
                        <li><span class="danger-text">Limite Negativo:</span> `EPS_CURRENT_LIMIT_NEGATIVE` (-2000 A)</li>
                    </ul>
                    <p class="text-sm mt-1">É crucial que a corrente de excitação do EPS esteja dentro desses limites para garantir a segurança e a integridade do equipamento durante o ensaio.</p>
                </div>
                <div class="chart-container mt-3">
                    <div class="section-title-box">Visualização dos Limites de Corrente do EPS</div>
                    <canvas id="epsCurrentLimitsChart"></canvas>
                </div>
            </div>
        </div>
        <div class="footer">A plataforma garante que o ensaio em vazio seja realizado dentro de parâmetros seguros e confiáveis.</div>
    </div>

    <!-- Slide Perdas em Vazio - Análise EPS Tabela -->
    <div class="slide-container" id="slide-no-load-losses-analysis-table">
        <div class="slide-title">Perdas em Vazio: Análise de Limites do EPS</div>
        <div class="info-box">
            <p>Avaliação dos limites de potência e corrente do EPS para diferentes níveis de tensão de ensaio durante o teste em vazio:</p>
        </div>
        <div class="table-container">
            <table>
                <thead><tr><th>Nível Tensão (p.u.)</th><th>S<sub>ensaio</sub> Estimada (kVA)</th><th>% Limite EPS (1450kVA)</th><th>Status Potência EPS</th><th>Status Corrente SUT/EPS</th></tr></thead>
                <tbody>
                    <tr><td>1.0</td><td>597</td><td>41.2%</td><td class="success-text">OK</td><td class="success-text">OK</td></tr>
                    <tr><td>1.1</td><td>1313</td><td>90.6%</td><td class="warning-text">ALERTA</td><td class="warning-text">OK (Alguns taps SUT no limite)</td></tr>
                    <tr><td>1.2</td><td>2868</td><td>197.8%</td><td class="danger-text">EXCEDE LIMITE</td><td class="danger-text">EXCEDE LIMITE</td></tr>
                </tbody>
            </table>
        </div>
        <div class="chart-container mt-3">
             <canvas id="noLoadLossesPowerChart"></canvas>
        </div>
        <div class="footer">O gráfico visualiza os limites de potência do EPS em relação à demanda do ensaio.</div>
    </div>
    
    <!-- Slide Simulador Perdas em Vazio (NOVA VERSÃO) -->
    <!-- Slide Simulador Perdas em Vazio (NOVA VERSÃO) -->
<div class="slide-container" id="slide-no-load-losses-simulator">
<div class="slide-title">Simulador Interativo: Perdas em Vazio</div>
<div class="content-section simulator-container" id="vazio-tab-pane">
 
<!-- Seção Superior: Diagrama do Sistema -->
        <div class="row align-items-center justify-content-center text-center position-relative diagram-vazio-container">
            
            <div class="vazio-wire-container">
                <div id="vazio-main-wire" class="vazio-wire"></div>
            </div>

            <!-- NOVO: Match Transformer -->
            <div class="col-md-6 col-lg-3">
                <div id="vazio-match-card" class="card shadow-sm h-100">
                    <h5 class="card-header"><i class="fas fa-exchange-alt"></i> MATCH TRANSFORMER</h5>
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted text-start">ENTRADA</h6>
                        <div class="vazio-data-row"><label for="vazio-match-input-voltage">V:</label><input type="text" readonly class="form-control-plaintext" id="vazio-match-input-voltage" value="380"></div>
                        <div class="vazio-data-row"><label for="vazio-match-input-current">I:</label><input type="text" readonly class="form-control-plaintext" id="vazio-match-input-current" value="2328.3"></div>
                        <hr>
                        <h6 class="card-subtitle mb-2 text-muted text-start">SAÍDA</h6>
                        <div class="vazio-data-row"><label for="vazio-match-output-voltage">V:</label><input type="text" readonly class="form-control-plaintext" id="vazio-match-output-voltage" value="480"></div>
                        <div class="vazio-data-row"><label for="vazio-match-output-current">I:</label><input type="text" readonly class="form-control-plaintext" id="vazio-match-output-current" value="1843.2"></div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div id="vazio-eps-card" class="card shadow-sm h-100">
                    <h5 class="card-header"><i class="fas fa-bolt"></i> EPS SYSTEM</h5>
                    <div class="card-body">
                        <!-- A entrada do EPS (480V) não é exibida, apenas sua saída -->
                        <div class="vazio-data-row"><label for="vazio-eps-voltage">Tensão (V):</label><input type="text" readonly class="form-control-plaintext" id="vazio-eps-voltage" value="4.8"></div>
                        <div class="vazio-data-row"><label for="vazio-eps-current">Corrente (A):</label><input type="text" readonly class="form-control-plaintext" id="vazio-eps-current" value="20.0"></div>
                        <div class="vazio-data-row"><label for="vazio-eps-kva">Potência (kVA):</label><input type="text" readonly class="form-control-plaintext" id="vazio-eps-kva" value="0.1"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3">
                <div id="vazio-sut-card" class="card shadow-sm h-100">
                    <h5 class="card-header"><i class="fas fa-random"></i> SUT</h5>
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted text-start">ENTRADA</h6>
                        <div class="vazio-data-row"><label for="vazio-sut-input-voltage">V:</label><input type="text" readonly class="form-control-plaintext" id="vazio-sut-input-voltage" value="4.8"></div>
                        <div class="vazio-data-row"><label for="vazio-sut-input-current">I:</label><input type="text" readonly class="form-control-plaintext" id="vazio-sut-input-current" value="20.0"></div>
                        <hr>
                        <h6 class="card-subtitle mb-2 text-muted text-start">SAÍDA</h6>
                        <div class="vazio-data-row"><label for="vazio-sut-output-voltage">V (kV):</label><input type="text" readonly class="form-control-plaintext" id="vazio-sut-output-voltage" value="0.1"></div>
                        <div class="vazio-data-row"><label for="vazio-sut-output-current">I (A):</label><input type="text" readonly class="form-control-plaintext" id="vazio-sut-output-current" value="0.6"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3">
                <div id="vazio-dut-card" class="card shadow-sm h-100">
                    <h5 class="card-header bg-custom-orange"><i class="fas fa-industry"></i> CARGA (DUT)</h5>
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted text-start">ENTRADA</h6>
                        <div class="vazio-data-row"><label for="vazio-dut-input-voltage">V (kV):</label><input type="text" readonly class="form-control-plaintext" id="vazio-dut-input-voltage" value="0.1"></div>
                        <div class="vazio-data-row"><label for="vazio-dut-input-current">I (A):</label><input type="text" readonly class="form-control-plaintext" id="vazio-dut-input-current" value="0.6"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Seção Inferior: Controles e Status -->
        <div class="row justify-content-center g-4 mt-3">
            <div class="col-lg-7">
                <div class="card shadow-sm h-100">
                    <h5 class="card-header"><i class="fas fa-sliders-h"></i> Painel de Controle</h5>
                    <div class="card-body d-flex flex-wrap justify-content-start align-items-end gap-5 px-4">
                        <div>
                            <h6 class="text-start mb-1">Potência da Fonte</h6>
                            <div class="input-group">
                                <button id="vazio-btn-down" class="btn btn-danger"><i class="fas fa-caret-down"></i></button>
                                <input type="text" class="form-control text-center fw-bold" style="max-width: 60px;" id="vazio-power-level-display" value="1" readonly>
                                <button id="vazio-btn-up" class="btn btn-success"><i class="fas fa-caret-up"></i></button>
                            </div>
                            <div class="mt-1 small text-muted text-center">%</div>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-start mb-1">Condição do Núcleo (DUT)</h6>
                            <select class="form-select" id="vazio-core-selector" aria-label="Seletor de condição do núcleo">
                                <option value="good" selected>Núcleo Bom</option>
                                <option value="bad_current">Falha: Limite de Corrente</option>
                                <option value="bad_power">Falha: Limite de Potência</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-5">
                <div class="card shadow-sm h-100">
                    <h5 class="card-header"><i class="fas fa-info-circle"></i> Status do Sistema</h5>
                    <div class="card-body d-flex flex-column justify-content-center text-center">
                        <div id="vazio-system-status-card" class="alert alert-success mb-0">
                            <h5 class="alert-heading" id="vazio-status-label">OPERAÇÃO NOMINAL</h5>
                            <p class="mb-0" id="vazio-status-message">O sistema está operando dentro dos parâmetros esperados.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="footer">Este simulador ilustra a relação entre a fonte (EPS), SUT e DUT no ensaio em vazio.</div>
</div>
    
    <!-- NOVO SLIDE: Introdução ao Banco de Capacitores -->
    <div class="slide-container" id="slide-capacitor-bank-introduction">
        <div class="slide-title">O Compensador de Potência: Banco de Capacitores</div>
        <div class="content-container">
            <div class="left-section">
                <div class="info-box">
                    <div class="section-title-box">O Problema: Cargas Altamente Indutivas</div>
                    <p>No ensaio de <span class="highlight-text">Perdas em Carga</span>, o transformador (DUT) está em curto-circuito. Isso o transforma numa carga quase puramente indutiva (baixo fator de potência).</p>
                    <p>Para circular a corrente nominal, o sistema precisa fornecer uma <span class="danger-text">enorme quantidade de Potência Reativa (MVAr)</span>, muito maior que a Potência Ativa (kW) das perdas.</p>
                    <p>Tentar suprir essa demanda apenas com o EPS o <span class="warning-text">sobrecarregaria e inviabilizaria o ensaio</span>.</p>
                </div>
            </div>
            <div class="right-section">
                <div class="info-box">
                    <div class="section-title-box">A Solução: Compensação Reativa</div>
                    <p>O <strong>Banco de Capacitores</strong> é conectado em paralelo com o DUT. Sua função é "injetar" a potência reativa que a carga indutiva demanda.</p>
                    <div class="key-point mt-2">
                        <strong class="success-text">Resultado:</strong> O banco de capacitores "alivia" o EPS, que passa a fornecer apenas a pequena parcela de potência ativa (perdas) e a diferença residual de reativos. Isso torna o ensaio <span class="success-text">viável e seguro</span>.
                    </div>
                </div>
                <div class="info-box">
                    <div class="section-title-box">Inteligência da Plataforma</div>
                    <p>A plataforma executa uma análise complexa para encontrar a configuração ótima do banco, considerando:</p>
                    <ul class="list-disc pl-4 text-sm">
                        <li>Tensão nominal do banco.</li>
                        <li>Número de unidades capacitivas em série/paralelo.</li>
                        <li>Combinação das chaves internas (Q1-Q5) para ajuste fino da potência.</li>
                        <li>Limites de corrente e potência do EPS e do próprio banco.</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer">O gerenciamento inteligente do Banco de Capacitores é a chave para o sucesso do ensaio de perdas em carga.</div>
    </div>

    <!-- Slide Perdas em Carga - Introdução -->
    <div class="slide-container" id="slide-load-losses-intro">
        <div class="slide-title">Perdas em Carga: Contexto e Desafio Numérico</div>
        <div class="single-column-section">
            <div class="info-box">
                <div class="section-title-box">Objetivo e Principal Desafio</div>
                <p>Medir perdas Joule (I²R) e adicionais sob corrente nominal. O DUT é curto-circuitado no secundário e alimentado com tensão reduzida (V<sub>cc</sub>) no primário.</p>
                <p class="mt-2"><span class="highlight-text">Desafio Principal:</span> O circuito é <span class="danger-text">altamente indutivo</span> (FP baixo, ex: 0.05-0.2). Para circular a I<sub>nominal</sub> (alta), a demanda de <span class="warning-text">Potência Reativa (MVAr) é muito superior à Potência Ativa (kW)</span>.</p>
                <div class="key-point mt-2"><strong class="warning-text">Consequência:</strong> Sem compensação, a alta corrente reativa sobrecarregaria o EPS (limites em kVA e Amperes). A documentação mostra que um ensaio de perdas em carga pode exigir <span class="font-mono">Vários kVA</span>, que pode superar o limite total do EPS de 1450 kVA.</div>
            </div>
            <div class="formula-box">
                <div class="section-title-box">Potência Reativa de Teste (Q<sub>teste</sub>)</div>
                <div class="formula">S<sub>teste_MVA</sub> = V<sub>cc_kV_cenário</sub> × I<sub>nom_A_tap_DUT</sub> × Fator<sub>Tipo</sub> / 1000</div>
                <div class="formula">Q<sub>teste_MVAr</sub> = √(S<sub>teste_MVA</sub>² - (P<sub>carga_kW_sem_vazio</sub>/1000)²)</div>
            </div>
             <div class="info-box">
                <div class="section-title-box">Solução da Plataforma: Banco de Capacitores Inteligente</div>
                <p>O <span class="highlight-text">Banco de Capacitores</span> é conectado em paralelo com o DUT para fornecer a maior parte da potência reativa necessária (Q<sub>banco_efetiva</sub>), aliviando drasticamente a carga sobre o EPS.</p>
                <div class="key-point mt-1"><strong class="warning-text">Análise Detalhada:</strong> A plataforma otimiza a seleção de V<sub>banco</sub>, a configuração das unidades capacitivas (CPs) e o acionamento das chaves internas (Q/CS) para minimizar a corrente no EPS e garantir um ensaio seguro.</div>
            </div>
        </div>
        <div class="footer">Lógica central: `losses_service.py`. Constantes: `constants.py`.</div>
    </div>

    <!-- Slide Perdas em Carga - Seleção da Tensão do Banco -->
    <div class="slide-container" id="slide-load-losses-bank-selection">
        <div class="slide-title">Perdas em Carga: Estratégia de Seleção da Tensão do Banco</div>
        <div class="single-column-section">
            <div class="info-box">
                <div class="section-title-box">Estratégias de Seleção da Tensão Nominal do Banco</div>
                <p>Para cada V<sub>cc_kV_cenário</sub>, a plataforma analisa até duas estratégias:</p>
                <div class="key-point mt-2">
                    <strong class="warning-text">1. Banco V≤ (Sem Fator - SF):</strong>
                    <p class="mt-1">Seleciona V<sub>banco_SF</sub> = menor V<sub>banco_disp</sub> ≥ V<sub>cc_kV_cenário</sub>.</p>
                </div>
                <div class="key-point mt-1">
                    <strong class="warning-text">2. Banco V> (Com Fator - CF):</strong>
                    <p class="mt-1">Busca V<sub>banco_CF</sub> < V<sub>cc_kV_cenário</sub> tal que V<sub>cc_kV_cenário</sub> ≤ V<sub>banco_CF</sub> × <code>FACTOR_CAP_BANC_OVERVOLTAGE</code> (1.1).</p>
                </div>
                <p class="italic mt-1">Função: `select_target_bank_voltage_keys()`.</p>
            </div>
            <div class="formula-box">
                <div class="section-title-box">Potência Reativa Efetiva do Banco</div>
                <div class="formula">Q<sub>efetiva_MVAr</sub> = Q<sub>banco_nominal_configurada</sub> × (V<sub>cc_kV_cenário</sub> / V<sub>banco_nominal_selecionada</sub>)²</div>
            </div>
             <div class="table-container">
                <div class="section-title-box text-sm">Tensões de Banco Disponíveis no EPS</div>
                <table><thead><tr><th>EPS Trifásico (kV)</th><th>EPS Monofásico (kV)</th></tr></thead>
                    <tbody><tr><td>13.8, 23.9, 27.6, 41.4, 47.8, 55.2, 71.7, 95.6</td><td>13.8, 27.6, 41.4, 55.2</td></tr></tbody>
                </table>
            </div>
        </div>
        <div class="footer">A seleção inteligente de V<sub>banco</sub> otimiza Q<sub>efetiva</sub>.</div>
    </div>

    <!-- Slide Perdas em Carga - Estrutura do Banco -->
    <div class="slide-container" id="slide-load-losses-bank-structure">
        <div class="slide-title">Perdas em Carga: Estrutura Física e Lógica do Banco</div>
<!-- INÍCIO DO BLOCO DE CÓDIGO DO DIAGRAMA GERAL -->
            <div class="info-box mt-3">
                <div class="section-title-box">Diagrama Geral do Banco de Capacitores</div>
                <img src="assets/complete_cap_bank.png" alt="Diagrama completo do banco de capacitores mostrando as três fases, as chaves de manobra CS e as unidades capacitivas CP" class="img-fluid rounded border">
                <p class="text-center mt-2 text-muted small">Visão geral da interconexão das unidades (CP) através das chaves de manobra (CS) para as três fases do sistema.</p>
            </div>
            <!-- FIM DO BLOCO DE CÓDIGO DO DIAGRAMA GERAL -->
        <div class="single-column-section">
            <div class="info-box">
                <div class="section-title-box">Unidades Capacitivas (CPxAy) e Strings</div>
                <p>O banco é modular. Unidades físicas (CP<span class="font-bold warning-text">x</span>P<span class="font-bold warning-text">y</span>U<span class="font-bold warning-text">z</span>) são conectadas em série para formar uma "string" que suporta a V<sub>banco_nominal</sub>.</p>
                <ul class="list-disc pl-5 space-y-1">
                    <li><strong>Unidades em Série por String:</strong> O número de unidades em série numa string depende da V<sub>banco_nominal</sub>. Ex: Para 27.6kV, são 2 unidades. <span class="italic">(Ref: `UNITS_PER_SERIES_STRING_BY_VOLTAGE_KV`)</span></li>
                    <li><strong>Strings em Paralelo:</strong> Para aumentar a Q<sub>total</sub>, múltiplas strings podem ser conectadas em paralelo por fase.</li>
                    <li><strong>Disponibilidade:</strong> `CAPACITORS_BY_VOLTAGE_TRI/MONO` lista unidades disponíveis para cada V<sub>banco</sub>.</li>
                </ul>
            </div>
            <div class="info-box">
                <div class="section-title-box">Grupos Lógicos de Unidades (Uz=1 ou Uz=2)</div>
                <p>Unidades são organizadas em "Grupo 1" (CPxPy<span class="font-bold danger-text">1</span>) e "Grupo 2" (CPxPy<span class="font-bold danger-text">2</span>).</p>
                <ul class="list-disc pl-5 space-y-1">
                    <li><strong>Prioridade "Grupo 1":</strong> Tenta atender Q<sub>banco_nec</sub> com unidades do Grupo 1.</li>
                    <li><strong>Escalonamento para "Grupo 1+2":</strong> Se Grupo 1 for insuficiente, considera Grupo 1 + Grupo 2.</li>
                </ul>
            </div>

<!-- FIM DO BLOCO DE CÓDIGO DO DIAGRAMA GERAL -->
        </div>
        <div class="footer">A estrutura modular permite escalabilidade e flexibilidade.</div>
    </div>

    <!-- Slide Perdas em Carga - Chaves Q -->
    <div class="slide-container" id="slide-load-losses-q-switches">
        <div class="slide-title">Perdas em Carga: Chaves Q e Degraus de Potência Reativa</div>
        <div class="single-column-section">
            <div class="info-box">
                <div class="section-title-box">Potência Reativa por Unidade Física (CPxPyUz)</div>
                <p>Cada unidade física possui 5 chaves internas (Q1 a Q5) para degraus de potência nominal:</p>
                <div class="text-center my-3">
                    <div class="switch"><span class="switch-icon"><i class="fas fa-plug"></i></span><span>Q1: 0.1 MVAr</span></div>
                    <div class="switch"><span class="switch-icon"><i class="fas fa-plug"></i></span><span>Q2: 0.2 MVAr</span></div>
                    <div class="switch"><span class="switch-icon"><i class="fas fa-plug"></i></span><span>Q3: 0.8 MVAr</span></div>
                    <div class="switch"><span class="switch-icon"><i class="fas fa-plug"></i></span><span>Q4: 1.2 MVAr</span></div>
                    <div class="switch"><span class="switch-icon"><i class="fas fa-plug"></i></span><span>Q5: 1.6 MVAr</span></div>
                </div>
                <p>Total por unidade física: <span class="font-bold highlight-text">3.9 MVAr</span> (Soma Q1-Q5). <span class="text-sm italic">(Ref: `Q_SWITCH_POWERS["generic_cp"]`)</span></p>
            </div>
<!-- INÍCIO DO BLOCO DE CÓDIGO DAS IMAGENS DAS CHAVES Q -->
<div class="info-box">
    <div class="row g-4 align-items-center">
        <div class="col-md-6">
            <img src="assets/0MVAr_cap_bank.png" alt="Diagrama de uma unidade de capacitor com chaves Q desligadas" class="img-fluid rounded shadow-sm border">
            <p class="text-center mt-2 text-muted small"><strong>Figura 1:</strong> Unidade capacitiva com chaves Q abertas. Potência reativa = <strong>0 MVAr</strong>.</p>
        </div>
        <div class="col-md-6">
            <img src="assets/1.7MVAr_cap_bank.png" alt="Diagrama de uma unidade de capacitor com chaves Q ligadas" class="img-fluid rounded shadow-sm border">
            <p class="text-center mt-2 text-muted small"><strong>Figura 2:</strong> Mesma unidade com uma combinação de chaves Q fechadas, resultando em <strong>1.7 MVAr</strong>.</p>
        </div>
    </div>
</div>
<!-- FIM DO BLOCO DE CÓDIGO DAS IMAGENS DAS CHAVES Q -->
            <div class="info-box">
                <div class="section-title-box">Combinações de Chaves Q</div>
                <p>Para atingir Q<sub>banco_nec</sub> por unidade, a plataforma testa todas as 31 combinações possíveis de Q1-Q5 (mais "nenhuma ligada").</p>
                <p class="text-sm italic mt-1">Funções: `generate_q_combinations()`, `calculate_q_combination_power()`.</p>
            </div>
        </div>
        <div class="footer">Controle fino das chaves Q permite ajuste preciso da compensação.</div>
    </div>

    <!-- Slide Perdas em Carga - Limites de Grupo -->
    <div class="slide-container" id="slide-load-losses-group-limits">
        <div class="slide-title">Perdas em Carga: Limites de Potência Reativa Nominal</div>
        <div class="single-column-section">
            <div class="info-box">
                <p>O Banco de Capacitores possui limites máximos de <span class="highlight-text">potência reativa nominal</span> (MVAr) que pode fornecer, dependendo da V<sub>banco_nominal</sub> e do grupo lógico (G1 ou G1+2).</p>
            </div>
            <div class="table-container text-xs">
                <div class="section-title-box text-sm">Tabela de Limites (`constants.CAPACITOR_POWER_LIMITS_BY_VOLTAGE`)</div>
                <table>
                    <thead><tr><th>V<sub>banco</sub> (kV)</th><th>Grupo</th><th>Q<sub>min_nom</sub> (MVAr)</th><th>Q<sub>máx_nom</sub> (MVAr)</th></tr></thead>
                    <tbody>
                        <tr><td rowspan="2" class="align-middle font-semibold">13.8</td><td>Grupo 1</td><td>0.3</td><td>11.7</td></tr>
                        <tr><td>Grupo 1+2</td><td>0.6</td><td>23.4</td></tr>
                        <tr><td rowspan="2" class="align-middle font-semibold">27.6</td><td>Grupo 1</td><td>1.2</td><td>46.8</td></tr>
                        <tr><td>Grupo 1+2</td><td>2.4</td><td>93.6</td></tr>
                        <tr><td rowspan="2" class="align-middle font-semibold">41.4</td><td>Grupo 1</td><td>0.9</td><td>35.1</td></tr>
                        <tr><td>Grupo 1+2</td><td>1.8</td><td>70.2</td></tr>
                        <!-- Outras tensões omitidas para brevidade -->
                    </tbody>
                </table>
            </div>
            <div class="info-box mt-3">
                <p>Durante a otimização, se uma configuração de Q e strings resulta em Q<sub>nominal_fornecida_total</sub> > Q<sub>máx_nom</sub> do grupo/tensão, ela é <span class="danger-text">descartada</span>.</p>
            </div>
        </div>
        <div class="footer">Respeitar os limites nominais do banco é essencial.</div>
    </div>

    <!-- Slide Perdas em Carga - Otimização -->
    <div class="slide-container" id="slide-load-losses-optimization">
        <div class="slide-title">Perdas em Carga: Algoritmo de Otimização e Curadoria de Soluções</div>
        <div class="single-column-section">
            <div class="info-box">
                <div class="section-title-box">Processo de Busca e Ranking (`_get_curated_bank_options`)</div>
                <ol class="list-decimal ml-5 space-y-1">
                    <li><strong>Geração de Candidatos:</strong> Itera chaves Q e nº de strings paralelas.</li>
                    <li><strong>Cálculo de Desempenho:</strong> Para cada candidato, calcula Q<sub>efetiva</sub>, I<sub>cap</sub>, I<sub>EPS_net</sub>.</li>
                    <li><strong>Pré-Filtragem:</strong> Agrupa por Q<sub>nom_fornecida</sub> e seleciona a melhor solução.</li>
                    <li><strong>Curadoria e Ranking Final (até 5 opções):</strong> Ordena pela "qualidade geral":
                        <ol class="list-alpha ml-3">
                            <li>EPS OK <span class="font-bold success-text">E</span> Potência Ideal Atendida.</li>
                            <li>EPS OK <span class="font-bold success-text">E</span> Potência Mínima Atendida.</li>
                            <li>EPS OK (mesmo subcompensado).</li>
                            <li>Menor I<sub>EPS_net_A</sub> (abs).</li>
                            <li>Q<sub>nom_fornecida</sub> mais próxima da Q<sub>banco_nec_nom</sub>.</li>
                        </ol>
                    </li>
                    <li>Seleciona melhor solução geral como padrão e até 4 alternativas.</li>
                </ol>
            </div>
        </div>
        <div class="footer">O objetivo é eficácia, simplicidade e segurança do EPS.</div>
    </div>

    <!-- Slide Perdas em Carga - Chaves CS -->
    <div class="slide-container" id="slide-load-losses-cs-keys">
        <div class="slide-title">Perdas em Carga: Configuração das Chaves de Manobra (CS)</div>
        <div class="single-column-section">
            <div class="info-box">
                <div class="section-title-box">Função das Chaves CS</div>
                <p>Após otimizar unidades (CPxPyUz) e chaves Q, a plataforma define as <span class="highlight-text">Chaves de Manobra (CS)</span> para conectar a configuração ao barramento.</p>
            </div>
            <div class="info-box">
                <div class="section-title-box">Lógica de Ativação (`get_cs_configuration`)</div>
                <ul class="list-disc pl-5 space-y-1">
                    <li><strong>Unidades CPxPyUz Selecionadas:</strong> Se CP1A1 e CP2A1 estão em uso, CS1A1 e CS2A1 são ativadas.</li>
                    <li><strong>Tensão Nominal do Banco:</strong> Mapeamentos em `constants.py` listam CSs gerais permitidas.</li>
                </ul>
                <p class="mt-2"><strong>Exemplo (Trifásico, 13.8kV, usando todas as unidades CP2...):</strong>
                <br><span class="text-sm font-mono success-text">CS Ativas: CSA,B,C, CS1A1,1A2,...2C1,2C2, CS7A,B,C.</span>
                </p>
            </div>
        </div>
        <div class="footer">Configuração correta das CSs é vital para a operação segura.</div>
    </div>

    <!-- Slide Perdas em Carga - Fator de Tipo (EPS) -->
    <div class="slide-container" id="slide-load-losses-type-factor">
        <div class="slide-title">Perdas em Carga: Impacto do Fator de Tipo na Corrente do EPS</div>
        <div class="single-column-section">
            <div class="info-box" style="background-color: #fbecec; border-left-color: var(--cor-perigo); border-left-width: 5px;">
                <div class="section-title-box" style="color: var(--cor-perigo);">Fator de Tipo na Corrente do EPS (I<sub>EPS_net</sub>)</div>
                <p>A corrente no lado de baixa tensão do SUT (entrada do EPS) é calculada como:</p>
                <div class="formula">I<sub>EPS_net_A</sub> = (I<sub>DUT_A</sub> - I<sub>cap_banco_A</sub>) × Ratio<sub>SUT</sub> × <span class="font-bold danger-text">Fator<sub>Tipo_EPS</sub></span></div>
                 <ul class="list-disc pl-5 space-y-1 mt-1">
                    <li>Ensaio <span class="font-semibold success-text">Trifásico</span> no DUT: <span class="font-mono success-text">Fator<sub>Tipo_EPS</sub> = 1.0</span></li>
                    <li>Ensaio <span class="font-semibold danger-text">Monofásico</span> no DUT: <span class="font-mono danger-text">Fator<sub>Tipo_EPS</sub> = √3 (≈ 1.732)</span></li>
                </ul>
                <p class="mt-2"><span class="warning-text">Implicação Crítica:</span> Para <span class="highlight-text">mesma potência monofásica</span> de teste no DUT, a corrente no EPS será <span class="danger-text">√3 vezes maior</span> vs. um cenário trifásico de potência por fase equivalente.</p>
            </div>
        </div>
        <div class="footer">O fator √3 para ensaios monofásicos é crucial para não subestimar I<sub>EPS</sub>.</div>
    </div>

    <!-- Slide Perdas em Carga - Corrente Final EPS -->
    <div class="slide-container" id="slide-load-losses-final-eps">
        <div class="slide-title">Perdas em Carga: Análise Final da Corrente Compensada no EPS</div>
        <div class="single-column-section">
            <div class="info-box">
                <div class="section-title-box">A Prova Final: Corrente Residual no EPS (I<sub>EPS_net</sub>)</div>
                <p>Após otimizar o banco, o passo final é calcular I<sub>EPS_net</sub> e validar contra os limites.</p>
            </div>
            <div class="formula-box">
                <div class="section-title-box">Revisão do Cálculo de I<sub>EPS_net</sub></div>
                <div class="formula">I<sub>EPS_net_A</sub> = (I<sub>teste_DUT_A</sub> - I<sub>cap_banco_A</sub>) × Ratio<sub>SUT</sub> × Fator<sub>Tipo_EPS</sub></div>
                <p class="formula-description">Ratio<sub>SUT</sub> = (V<sub>SUT_tap_AT_sel_V</sub> / 480V)</p>
            </div>
            <div class="info-box">
                <div class="section-title-box">Verificação de Limites e Status</div>
                <p>I<sub>EPS_net</sub> é comparada com:</p>
                <ul class="list-disc pl-5 space-y-1">
                    <li>`EPS_CURRENT_LIMIT_POSITIVE` (+2000 A)</li>
                    <li>`EPS_CURRENT_LIMIT_NEGATIVE` (-2000 A)</li>
                </ul>
                <p class="mt-1">Status: <span class="success-text">OK</span>, <span class="warning-text">ALERTA</span>, <span class="danger-text">EXCEDE</span>.</p>
            </div>
        </div>
        <div class="footer">Análise da I<sub>EPS_net</sub> é o veredito da viabilidade e segurança do ensaio.</div>
    </div>

    <!-- Slide Perdas em Carga - Tabela de Resultados -->
    <div class="slide-container" id="slide-load-losses-results-table">
        <div class="slide-title">Perdas em Carga: Tabela de Resultados Interativa</div>
        <div class="content-section">
            <p>A plataforma consolida os resultados em tabelas interativas. Cada tabela cobre os taps Nominal, Menor e Maior do DUT. O usuário pode <span class="highlight-text">selecionar diferentes configurações de banco</span> para observar o impacto na I<sub>EPS</sub>.</p>
        </div>
        <div class="table-container text-xs">
             <p class="text-center font-semibold mb-1">Exemplo: Tabela para Medição a 25°C</p>
            <div class="overflow-x-auto">
                 <table class="min-w-full">
                    <thead>
                        <tr><th rowspan="2" class="sticky left-0 bg-gray-800 z-10">Tap DUT / Cenário</th><th rowspan="2">V<sub>cc</sub> (kV)</th> <th rowspan="2">I<sub>teste</sub> (A)</th> <th rowspan="2">P<sub>atv</sub> (kW)</th> <th rowspan="2">Q<sub>teste</sub> (MVAr)</th><th colspan="6" class="text-center border-start border-end">Banco V≤ (Opções Selecionáveis)</th><th colspan="6" class="text-center border-start border-end">Banco V> (Opções Selecionáveis)</th><th rowspan="2">Status V≤</th> <th rowspan="2">Status V></th></tr>
                        <tr><th>V<sub>bn</sub></th><th>CS</th><th>Q<sub>cfg</sub></th><th>Q<sub>n</sub></th><th>Q<sub>ef</sub></th><th>I<sub>cap</sub></th><th>V<sub>bn</sub></th><th>CS</th><th>Q<sub>cfg</sub></th><th>Q<sub>n</sub></th><th>Q<sub>ef</sub></th><th>I<sub>cap</sub></th></tr>
                    </thead>
                    <tbody>
                        <tr> <td class="sticky left-0 bg-light z-10">Nominal</td><td>10.5</td><td>1250</td><td>85.0</td><td>21.2</td>
                            <td>13.8</td><td>...</td><td>Q1,2</td><td>5.0</td><td>4.1</td><td>170</td>
                            <td class="text-muted">N/A</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td>
                            <td class="success-text">OK</td><td class="text-muted">N/A</td></tr>
                        <tr> <td class="sticky left-0 bg-light z-10">Menor Tap</td><td>12.1</td><td>1350</td><td>95.2</td><td>28.8</td>
                            <td>13.8</td><td>...</td><td>Q1,3</td><td>7.5</td><td>6.3</td><td>225</td>
                            <td>12.0</td><td>...</td><td>Q1-3</td><td>10.0</td><td>10.2</td><td>360</td>
                            <td class="success-text">OK</td><td class="warning-text">ALERTA</td></tr>
                         <tr><td class="sticky left-0 bg-light z-10">Maior Tap</td><td>18.2</td><td>1600</td><td>150.5</td><td>47.5</td>
                            <td>23.9</td><td>...</td><td>Q1-5</td><td>25.0</td><td>14.5</td><td>345</td>
                            <td colspan="6" class="text-center danger-text italic">Inv.</td>
                            <td class="danger-text">CRÍTICO</td><td class="danger-text">INV.</td></tr>
                    </tbody>
                </table>
            </div>
             <p class="text-xs italic mt-1">Na aplicação, "CS", "Q<sub>cfg</sub>", etc., são atualizados com a seleção do usuário.</p>
        </div>
        <div class="footer">Interface: `losses.js`. Lógica: `losses_service.py`.</div>
    </div>

    <!-- Slide Perdas em Carga - Sub-Tabela SUT/EPS -->
    <div class="slide-container" id="slide-load-losses-sut-eps-subtable">
        <div class="slide-title">Perdas em Carga: Sub-Tabela Dinâmica de Análise SUT/EPS</div>
        <div class="single-column-section">
            <div class="info-box">
                <div class="section-title-box">Detalhe da Análise de Corrente no EPS</div>
                <p>Para <span class="highlight-text">cada cenário da tabela principal</span> e para <span class="highlight-text">cada configuração de banco selecionada</span>, uma sub-tabela dinâmica é gerada/atualizada, mostrando os taps do SUT e a corrente residual no EPS.</p>
            </div>
            <div class="table-container text-xs">
                 <div class="section-title-box text-sm">Exemplo: Sub-Tabela SUT/EPS</div>
                 <p class="text-xs text-center mb-1">V<sub>cc</sub>=10.5kV, I<sub>DUT</sub>=1250A. Banco V≤ selecionado: I<sub>cap</sub>=170A. I<sub>DUT</sub>-I<sub>cap</sub> = 1080A.</p>
                <table>
                    <thead><tr><th>Tap SUT AT (kV)</th><th>I<sub>EPS</sub> V≤ (A)</th><th>% Limite EPS</th><th>Status EPS V≤</th><th>I<sub>EPS</sub> V> (A)</th><th>% Limite EPS</th><th>Status EPS V></th></tr></thead>
                    <tbody>
                        <tr><td>7.0</td><td>1574</td><td>78.7%</td><td class="success-text">OK</td><td class="text-muted">-</td><td class="text-muted">-</td><td class="text-muted">N/A</td></tr>
                        <tr class="sut-tap-ideal-highlight"><td class="font-bold">10.5 (Ideal ⭐)</td><td class="danger-text">2362</td><td class="danger-text">118.1%</td><td class="danger-text">EXCEDE</td><td class="text-muted">-</td><td>...</td><td class="text-muted">N/A</td></tr>
                        <tr><td>14.0</td><td class="danger-text">3149</td><td>...</td><td class="danger-text">EXCEDE</td><td class="text-muted">-</td><td>...</td><td class="text-muted">N/A</td></tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="footer">Função JS: `generateHorizontalSutEpsTable()`.</div>
    </div>
    
    <!-- Slide Simulador Perdas em Carga (NOVA VERSÃO) -->
    <div class="slide-container" id="slide-load-losses-simulator">
        <h2 class="slide-title">Simulador Interativo: Perdas em Carga</h2>
        <div class="content-section simulator-container" id="carga-tab-pane">

            <!-- A sobreposição é corrigida adicionando z-index às colunas dos cards -->
            <div class="row align-items-stretch justify-content-center text-center position-relative mb-4" style="height: 400px;">
                <!-- Linhas visuais do circuito (ficam no fundo, z-index: 0) -->
                <div id="carga-wire-main" class="carga-wire"></div>
                <div id="carga-wire-t-junction-cap" class="carga-wire"></div>
                <div id="carga-wire-t-junction-dut" class="carga-wire"></div>

                <!-- Coluna Esquerda: EPS System (z-index: 1 para ficar na frente) -->
                <div class="col-md-4 d-flex justify-content-center align-items-center" style="position: relative; z-index: 1;">
                    <div class="card shadow-sm w-75">
                        <div class="card-header"><i class="fas fa-bolt" aria-hidden="true"></i> EPS SYSTEM</div>
                        <div class="card-body">
                            <p class="mb-1">Tensão (V): <span id="carga-eps-voltage" class="display-value">0.0</span></p>
                            <p class="mb-0">Corrente (A): <span id="carga-eps-current" class="display-value">0.0</span></p>
                        </div>
                    </div>
                </div>

                <!-- Coluna Central: SUT (z-index: 1 para ficar na frente) -->
                <div class="col-md-4 d-flex justify-content-center align-items-center" style="position: relative; z-index: 1;">
                    <div class="card shadow-sm w-75">
                        <div class="card-header"><i class="fas fa-random" aria-hidden="true"></i> SUT</div>
                        <div class="card-body">
                            <p class="mb-1">Tensão (kV): <span id="carga-sut-output-voltage" class="display-value">0.0</span></p>
                            <p class="mb-0">Corrente (A): <span id="carga-sut-output-current" class="display-value">0.0</span></p>
                        </div>
                    </div>
                </div>

                <!-- Coluna Direita: Capacitor e Carga (z-index: 1 para ficar na frente) -->
                <div class="col-md-4 d-flex flex-column align-items-center justify-content-around" style="position: relative; z-index: 1;">
                    <div class="card shadow-sm w-75">
                        <div class="card-header"><i class="fas fa-copyright" aria-hidden="true"></i> CAP</div>
                        <div class="card-body">
                            <p class="mb-0">Corrente (A): <span id="carga-capacitor-current-display" class="display-value">0.0</span></p>
                        </div>
                    </div>
                    <div class="card shadow-sm w-75">
                        <div class="card-header card-header-alert">
                            <i class="fas fa-industry" aria-hidden="true"></i> CARGA (DUT)
                        </div>
                        <div class="card-body">
                            <p class="mb-0">Demanda (A): <span class="display-value">640.0</span></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Painel de Controle e Status -->
            <div class="row justify-content-center g-4">
                <div class="col-md-8">
                    <div class="card shadow-sm">
                        <div class="card-header"><i class="fas fa-sliders-h" aria-hidden="true"></i> Painel de Controle</div>
                        <!-- justify-content-between e flex-nowrap garantem o alinhamento em uma linha -->
                        <div class="card-body d-flex justify-content-between align-items-center flex-nowrap px-3 gap-3">
                            <div class="text-center">
                                <h6>Potência da Fonte</h6>
                                <div class="d-flex align-items-center gap-2">
                                    <button type="button" id="carga-btn-down" class="btn btn-danger" aria-label="Diminuir potência"><i class="fas fa-caret-down" aria-hidden="true"></i></button>
                                    <span id="carga-power-level-display" class="display-value fs-4">0</span>
                                    <button type="button" id="carga-btn-up" class="btn btn-success" aria-label="Aumentar potência"><i class="fas fa-caret-up" aria-hidden="true"></i></button>
                                </div>
                                <div class="form-text mt-1">%</div>
                            </div>
                            <div class="text-center">
                                <h6>Tensão Nominal (V_cap)</h6>
                                <div class="btn-group" role="group" aria-label="Controle de Tensão Nominal">
                                    <button type="button" class="btn btn-outline-primary" id="carga-btn-v-cap-down" aria-label="Diminuir Tensão Nominal"><i class="fas fa-minus" aria-hidden="true"></i></button>
                                    <span id="carga-v-cap-display" class="btn btn-primary disabled" style="width: 100px;">14.0 kV</span>
                                    <button type="button" class="btn btn-outline-primary" id="carga-btn-v-cap-up" aria-label="Aumentar Tensão Nominal"><i class="fas fa-plus" aria-hidden="true"></i></button>
                                </div>
                            </div>
                            <div class="text-center">
                                <h6>Potência Reativa (Q_nom)</h6>
                                <div class="btn-group" role="group" aria-label="Controle de Potência Reativa">
                                    <button type="button" class="btn btn-outline-primary" id="carga-btn-q-cap-down" aria-label="Diminuir Potência Reativa"><i class="fas fa-minus" aria-hidden="true"></i></button>
                                    <span id="carga-q-cap-display" class="btn btn-primary disabled" style="width: 100px;">0 kVAR</span>
                                    <button type="button" class="btn btn-outline-primary" id="carga-btn-q-cap-up" aria-label="Aumentar Potência Reativa"><i class="fas fa-plus" aria-hidden="true"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header"><i class="fas fa-info-circle" aria-hidden="true"></i> Status Resumido</div>
                        <div class="card-body d-flex flex-column justify-content-center text-center">
                            <div id="carga-eps-status-card" class="alert alert-secondary" role="alert">
                                <h5 class="alert-heading" id="carga-eps-status-label">FONTE DESLIGADA</h5>
                                <p class="mb-0" id="carga-eps-status-message">Ajuste a potência para iniciar.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <footer class="footer">Este simulador demonstra o efeito da compensação reativa do banco de capacitores.</footer>
    </div>
    
    <!-- Slide Conclusão -->
    <div class="slide-container" id="slide-conclusion">
        <div class="slide-title">Conclusão - O Valor da Análise Preditiva e Otimizada</div>
        <div class="content-section">
            <p class="text-lg text-center mb-4">A Plataforma Inteligente de Planejamento é uma <span class="highlight-text">ferramenta estratégica</span> que integra conhecimento para:</p>
            <div class="row g-4 mt-3">
                <div class="col-md-6"><div class="info-box h-100"><div class="section-title-box"><i class="fas fa-shield-alt" style="color: var(--cor-sucesso);"></i>Segurança Aprimorada</div><p>Previne ativamente a programação de ensaios que excederiam os limites operacionais, protegendo o <span class="font-semibold danger-text">EPS</span>, o <span class="font-semibold warning-text">Banco</span> e o <span class="font-semibold">DUT</span>.</p></div></div>
                <div class="col-md-6"><div class="info-box h-100"><div class="section-title-box"><i class="fas fa-cogs" style="color: var(--cor-primaria);"></i>Eficiência Operacional</div><p>Otimiza a seleção e configuração dos equipamentos, sugerindo as soluções mais <span class="font-semibold success-text">econômicas</span> e <span class="font-semibold success-text">estáveis</span>.</p></div></div>
                <div class="col-md-6"><div class="info-box h-100"><div class="section-title-box"><i class="fas fa-lightbulb" style="color: var(--cor-alerta);"></i>Clareza e Decisão Informada</div><p>Traduz cálculos complexos em <span class="font-semibold highlight-text">recomendações e diagnósticos claros e acionáveis</span> para decisões rápidas e fundamentadas.</p></div></div>
                <div class="col-md-6"><div class="info-box h-100"><div class="section-title-box"><i class="fas fa-brain" style="color: var(--cor-info);"></i>Inteligência Embutida</div><p>Emula a expertise de um engenheiro especialista ao considerar hierarquias de decisão e cenários complexos de forma automatizada.</p></div></div>
            </div>
            <div class="info-box mt-3" style="background-color: rgba(52, 152, 219, 0.05);">
                <div class="section-title-box text-center">Impacto Estratégico na Operação do Laboratório</div>
                <p class="text-center">Esta plataforma eleva o planejamento de uma tarefa operacional para um <span class="highlight-text">processo proativo, otimizado e orientado por dados</span>, aumentando a produtividade e a segurança dos ativos.</p>
            </div>
        </div>
        <div class="footer">A inteligência da plataforma reside na sua capacidade de simular e prever o comportamento do sistema completo.</div>
    </div>

    <!-- Slide Q&A -->
    <div class="slide-container" id="slide-qa" style="justify-content: center; align-items: center; text-align: center;">
        <div class="title-main">Perguntas e Respostas</div>
        <div class="subtitle-main">Obrigado pela sua atenção!</div>
        <div class="info-box w-full md:w-2/3 lg:w-1/2" style="border-width: 2px; border-color: var(--cor-primaria);">
            <div class="agenda-item"><div class="agenda-icon"><i class="fas fa-envelope"></i></div><div><EMAIL></div></div>
            <div class="agenda-item"><div class="agenda-icon"><i class="fas fa-phone-alt"></i></div><div>+55 (31) 98454-9893</div></div>
        </div>
        <div class="footer-main" style="position: absolute; bottom: 40px; left: 40px; right: 40px;">
            <div class="logo-main"><i class="fas fa-industry text-5xl" style="color: var(--cor-texto-secundario);"></i></div>
            <div class="date-main">Apresentação Interna - Junho 2025</div>
        </div>
    </div>
  </div> <!-- main-content -->

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const body = document.body;
      const sidebarToggle = document.getElementById('sidebar-toggle');
      const toggleIcon = sidebarToggle.querySelector('i');
      const sidebarLinks = document.querySelectorAll('.sidebar ul li a');
      const searchInput = document.getElementById('searchInput');
      const mainContentScroller = document.getElementById('main-content-area');
      const slides = Array.from(document.querySelectorAll('.slide-container'));
      let searchHighlights = [];

      // Função para alternar o estado do sidebar
      function toggleSidebar() {
          body.classList.toggle('sidebar-collapsed');
          const isCollapsed = body.classList.contains('sidebar-collapsed');
          if (isCollapsed) {
              toggleIcon.classList.remove('fa-times');
              toggleIcon.classList.add('fa-bars');
              sidebarToggle.setAttribute('title', 'Expandir Menu');
          } else {
              toggleIcon.classList.remove('fa-bars');
              toggleIcon.classList.add('fa-times');
              sidebarToggle.setAttribute('title', 'Recolher Menu');
          }
          // Dispara um evento de resize para que os gráficos se ajustem
          window.dispatchEvent(new Event('resize'));
      }

      // Adiciona o evento ao botão de toggle
      if (sidebarToggle) {
          sidebarToggle.addEventListener('click', toggleSidebar);
      }
      
      // Inicia com o sidebar colapsado
      body.classList.add('sidebar-collapsed');
      toggleIcon.classList.remove('fa-times');
      toggleIcon.classList.add('fa-bars');
      sidebarToggle.setAttribute('title', 'Expandir Menu');


      function updateActiveLink() {
        let currentSlideId = '';
        const scrollMidPoint = mainContentScroller.scrollTop + (mainContentScroller.clientHeight / 3);

        for (const slide of slides) {
          if (slide.offsetTop <= scrollMidPoint && (slide.offsetTop + slide.offsetHeight) > scrollMidPoint) {
            currentSlideId = slide.id;
            break;
          }
        }
        if (!currentSlideId && mainContentScroller.scrollTop < 50 && slides.length > 0) {
             currentSlideId = slides[0].id;
        }
        
        sidebarLinks.forEach(link => {
          link.classList.toggle('active', link.getAttribute('href') === `#${currentSlideId}`);
        });
      }

      sidebarLinks.forEach(link => {
        link.addEventListener('click', function (e) {
          e.preventDefault();
          const targetId = this.getAttribute('href').substring(1);
          const targetElement = document.getElementById(targetId);
          if (targetElement) {
            mainContentScroller.scrollTo({ top: targetElement.offsetTop, behavior: 'smooth' });
            sidebarLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            // Opcional: fechar o sidebar após clicar em um link em telas pequenas
            if (window.innerWidth < 768 && !body.classList.contains('sidebar-collapsed')) {
                toggleSidebar();
            }
          }
        });
      });

      function clearSearchHighlights() {
        searchHighlights.forEach(mark => {
          const parent = mark.parentNode;
          if (parent) {
            parent.replaceChild(document.createTextNode(mark.textContent), mark);
            parent.normalize();
          }
        });
        searchHighlights = [];
        document.querySelectorAll('.sidebar ul li a.active-search-result').forEach(link => {
            link.classList.remove('active-search-result');
        });
      }

      function performSearch() {
        clearSearchHighlights();
        const query = searchInput.value.trim().toLowerCase();
        if (query.length < 3) return;
        
        let firstMatchElement = null;
        const matchedLinks = new Set();
        
        slides.forEach(slide => {
            const walker = document.createTreeWalker(slide, NodeFilter.SHOW_TEXT, null, false);
            let node;
            const nodesToModify = [];
            
            while (node = walker.nextNode()) {
                if (node.parentNode.nodeName === 'SCRIPT' || node.parentNode.nodeName === 'STYLE' || 
                    node.parentNode.classList.contains('search-highlight')) continue;
                
                const text = node.nodeValue;
                const lowerText = text.toLowerCase();
                
                if (lowerText.includes(query)) {
                    nodesToModify.push({originalNode: node, text: text, lowerText: lowerText});
                }
            }
            
            nodesToModify.forEach(({originalNode, text, lowerText}) => {
                const fragment = document.createDocumentFragment();
                let lastIndex = 0;
                let matchIndex;
                
                while ((matchIndex = lowerText.indexOf(query, lastIndex)) !== -1) {
                    fragment.appendChild(document.createTextNode(text.substring(lastIndex, matchIndex)));
                    
                    const mark = document.createElement('mark');
                    mark.className = 'search-highlight';
                    mark.textContent = text.substring(matchIndex, matchIndex + query.length);
                    fragment.appendChild(mark);
                    searchHighlights.push(mark);
                    
                    if (!firstMatchElement) {
                       firstMatchElement = mark;
                    }
                    
                    const slideLink = document.querySelector(`.sidebar ul li a[href="#${slide.id}"]`);
                    if(slideLink) {
                        matchedLinks.add(slideLink);
                    }
                    
                    lastIndex = matchIndex + query.length;
                }
                fragment.appendChild(document.createTextNode(text.substring(lastIndex)));
                
                if (originalNode.parentNode) {
                    originalNode.parentNode.replaceChild(fragment, originalNode);
                }
            });
        });
        
        matchedLinks.forEach(link => link.classList.add('active-search-result'));
        
        if (firstMatchElement) {
           const targetSlide = firstMatchElement.closest('.slide-container');
           if (targetSlide) {
               mainContentScroller.scrollTo({ top: targetSlide.offsetTop, behavior: 'smooth' });
               setTimeout(() => {
                  firstMatchElement.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
               }, 350);
           }
        }
      }
      
      let searchDebounceTimer;
      searchInput.addEventListener('input', () => {
        clearTimeout(searchDebounceTimer);
        searchDebounceTimer = setTimeout(performSearch, 400);
      });

      mainContentScroller.addEventListener('scroll', updateActiveLink);
      window.addEventListener('resize', updateActiveLink);
      
      const initialHash = window.location.hash;
      if (initialHash) {
          const targetElement = document.getElementById(initialHash.substring(1));
          if (targetElement) {
              mainContentScroller.scrollTo({ top: targetElement.offsetTop, behavior: 'auto' });
          }
      }
      updateActiveLink();

      const noLoadChartCanvas = document.getElementById('noLoadLossesPowerChart');
      if (noLoadChartCanvas) {
          new Chart(noLoadChartCanvas, {
              type: 'bar',
              data: {
                  labels: ['1.0 p.u.', '1.1 p.u.', '1.2 p.u.'],
                  datasets: [{
                      label: 'Potência de Ensaio (kVA)',
                      data: [597, 1313, 2868],
                      backgroundColor: [ 'rgba(40, 167, 69, 0.7)', 'rgba(255, 193, 7, 0.7)', 'rgba(220, 53, 69, 0.7)' ],
                      borderColor: [ 'var(--cor-sucesso)', 'var(--cor-alerta)', 'var(--cor-perigo)' ],
                      borderWidth: 1
                  }]
              },
              options: {
                  responsive: true, maintainAspectRatio: false,
                  scales: { 
                      y: { beginAtZero: true, ticks: {color: 'var(--cor-texto-secundario)'}, grid: {color: 'rgba(0,0,0,0.05)'} }, 
                      x: {ticks: {color: 'var(--cor-texto-secundario)'}, grid: {display: false}} 
                  },
                  plugins: { 
                      legend: { display: false },
                      tooltip: {
                          backgroundColor: '#343a40',
                          titleColor: '#ffffff',
                          bodyColor: '#ffffff',
                      }
                  }
              }
          });
      }
      
      // NOVA LÓGICA DO SIMULADOR DE PERDAS EM VAZIO
      const VazioSimulator = () => {
          const elements = {
              btnUp: document.getElementById('vazio-btn-up'), btnDown: document.getElementById('vazio-btn-down'),
              powerLevelDisplay: document.getElementById('vazio-power-level-display'),
              epsVoltage: document.getElementById('vazio-eps-voltage'), epsCurrent: document.getElementById('vazio-eps-current'),
              epsKva: document.getElementById('vazio-eps-kva'),
              sutInputVoltage: document.getElementById('vazio-sut-input-voltage'), sutInputCurrent: document.getElementById('vazio-sut-input-current'),
              sutOutputVoltage: document.getElementById('vazio-sut-output-voltage'), sutOutputCurrent: document.getElementById('vazio-sut-output-current'),
              dutInputVoltage: document.getElementById('vazio-dut-input-voltage'), dutInputCurrent: document.getElementById('vazio-dut-input-current'),
              coreSelector: document.getElementById('vazio-core-selector'),
              systemStatusCard: document.getElementById('vazio-system-status-card'),
              statusLabel: document.getElementById('vazio-status-label'),
              statusMessage: document.getElementById('vazio-status-message'),
              mainWire: document.getElementById('vazio-main-wire'),
              epsCard: document.getElementById('vazio-eps-card'),
              sutCard: document.getElementById('vazio-sut-card'),
              dutCard: document.getElementById('vazio-dut-card'),
          };

          if (Object.values(elements).some(el => el === null)) { return; }

          let powerLevel = parseInt(elements.powerLevelDisplay.value, 10);
          let coreState = elements.coreSelector.value;
          let powerIntervalId = null;

          const CONFIG = {
              MAX_EPS_V: 480, MAX_EPS_I: 2000, MAX_EPS_KVA: 960,
              SUT_V_RATIO: 15.0 / 480, SUT_I_RATIO: 2000 / 64.0,
          };

          function calculateValues(level, state) {
              const p = level / 100;
              let epsV = 0, epsI = 0, sutOutV_kV = 0, sutOutI = 0;
              switch (state) {
                  case 'good': 
                      epsV = CONFIG.MAX_EPS_V * p; 
                      epsI = CONFIG.MAX_EPS_I * p * p; // Non-linear current
                      sutOutV_kV = epsV * CONFIG.SUT_V_RATIO; 
                      sutOutI = epsI / CONFIG.SUT_I_RATIO; 
                      break;
                  case 'bad_current': 
                      epsV = CONFIG.MAX_EPS_V * p * (1 - p * 0.5); 
                      epsI = CONFIG.MAX_EPS_I * Math.pow(p, 0.4); 
                      sutOutV_kV = epsV * CONFIG.SUT_V_RATIO * 0.2; 
                      sutOutI = epsI / CONFIG.SUT_I_RATIO * 10; 
                      break;
                  case 'bad_power': 
                      epsV = CONFIG.MAX_EPS_V * p; 
                      epsI = CONFIG.MAX_EPS_I * Math.pow(p, 1.8); 
                      sutOutV_kV = (epsV * CONFIG.SUT_V_RATIO) * (1 - p * 0.3); 
                      sutOutI = (epsI / CONFIG.SUT_I_RATIO) * (1 - p * 0.2); 
                      break;
              }
              epsI = Math.min(epsI, CONFIG.MAX_EPS_I * 1.5); // Allow some overload for effect
              const kva = Math.min((epsV * epsI) / 1000, CONFIG.MAX_EPS_KVA * 1.5);
              return { epsV, epsI, kva, sutOutV_kV, sutOutI };
          }

          function updateSystem() {
              const values = calculateValues(powerLevel, coreState);
              elements.powerLevelDisplay.value = powerLevel;
              elements.epsVoltage.value = values.epsV.toFixed(1);
              elements.epsCurrent.value = values.epsI.toFixed(1);
              elements.epsKva.value = values.kva.toFixed(1);
              elements.sutInputVoltage.value = values.epsV.toFixed(1);
              elements.sutInputCurrent.value = values.epsI.toFixed(1);
              elements.sutOutputVoltage.value = values.sutOutV_kV.toFixed(1);
              elements.sutOutputCurrent.value = values.sutOutI.toFixed(1);
              elements.dutInputVoltage.value = values.sutOutV_kV.toFixed(1);
              elements.dutInputCurrent.value = values.sutOutI.toFixed(1);
              updateStatus(values);
              updateWire(values);
          }

          function updateWire(values) {
              elements.mainWire.className = 'vazio-wire';
              if (powerLevel > 0) {
                  elements.mainWire.classList.add('vazio-animated-wire');
                  const isCritical = values.kva >= CONFIG.MAX_EPS_KVA || values.epsI >= CONFIG.MAX_EPS_I;
                  elements.mainWire.classList.add(isCritical ? 'vazio-wire-color-critical' : 'vazio-wire-color-normal');
              }
          }

          function updateStatus(values) {
              elements.systemStatusCard.className = 'alert mb-0';
              [elements.epsCard, elements.sutCard, elements.dutCard].forEach(card => card.classList.remove('vazio-critical-flash'));
              const isFailureMode = coreState !== 'good';
              const isCritical = values.kva >= CONFIG.MAX_EPS_KVA || values.epsI >= CONFIG.MAX_EPS_I;
              const isWarning = values.kva > CONFIG.MAX_EPS_KVA * 0.85;

              if (powerLevel === 0) {
                  elements.systemStatusCard.classList.add('alert-secondary');
                  elements.statusLabel.textContent = 'SISTEMA DESLIGADO';
                  elements.statusMessage.textContent = 'Ajuste a potência para iniciar a simulação.';
              } else if (isCritical) {
                  elements.systemStatusCard.classList.add('alert-danger');
                  elements.statusLabel.textContent = 'FALHA CRÍTICA!';
                  elements.statusMessage.textContent = `Limites da fonte excedidos! Potência: ${values.kva.toFixed(0)} kVA, Corrente: ${values.epsI.toFixed(0)} A.`;
                  [elements.epsCard, elements.sutCard, elements.dutCard].forEach(card => card.classList.add('vazio-critical-flash'));
              } else if (isFailureMode) {
                  elements.systemStatusCard.classList.add('alert-warning');
                  elements.statusLabel.textContent = 'NÚCLEO EM FALHA';
                  elements.statusMessage.textContent = 'O DUT está operando em modo de falha, demandando corrente/potência anormal.';
              } else if (isWarning) {
                  elements.systemStatusCard.classList.add('alert-warning');
                  elements.statusLabel.textContent = 'ALERTA DE SOBRECARGA';
                  elements.statusMessage.textContent = `A potência da fonte (${values.kva.toFixed(0)} kVA) está acima de 85% da capacidade.`;
              } else {
                  elements.systemStatusCard.classList.add('alert-success');
                  elements.statusLabel.textContent = 'OPERAÇÃO NOMINAL';
                  elements.statusMessage.textContent = 'O sistema está operando dentro dos parâmetros esperados.';
              }
          }
          
          function changePower(amount) {
              powerLevel = Math.max(0, Math.min(100, powerLevel + amount));
              updateSystem();
          }
          function stopInterval() {
              clearInterval(powerIntervalId);
              powerIntervalId = null;
          }

          elements.btnUp.addEventListener('mousedown', () => { powerIntervalId = setInterval(() => changePower(1), 50); });
          elements.btnDown.addEventListener('mousedown', () => { powerIntervalId = setInterval(() => changePower(-1), 50); });
          document.addEventListener('mouseup', stopInterval);
          document.addEventListener('mouseleave', stopInterval);
          elements.coreSelector.addEventListener('change', (e) => {
              coreState = e.target.value;
              updateSystem();
          });
          updateSystem();
      };
      
      // NOVA LÓGICA DO SIMULADOR DE PERDAS EM CARGA
      const CargaSimulator = () => {
          const elements = {
              btnUp: document.getElementById('carga-btn-up'), btnDown: document.getElementById('carga-btn-down'),
              powerLevelDisplay: document.getElementById('carga-power-level-display'),
              epsVoltage: document.getElementById('carga-eps-voltage'), epsCurrent: document.getElementById('carga-eps-current'),
              sutOutputVoltage: document.getElementById('carga-sut-output-voltage'), sutOutputCurrent: document.getElementById('carga-sut-output-current'),
              btnVCapUp: document.getElementById('carga-btn-v-cap-up'), btnVCapDown: document.getElementById('carga-btn-v-cap-down'),
              btnQCapUp: document.getElementById('carga-btn-q-cap-up'), btnQCapDown: document.getElementById('carga-btn-q-cap-down'),
              vCapDisplay: document.getElementById('carga-v-cap-display'), qCapDisplay: document.getElementById('carga-q-cap-display'),
              capacitorCurrentDisplay: document.getElementById('carga-capacitor-current-display'),
              epsStatusCard: document.getElementById('carga-eps-status-card'),
              epsStatusLabel: document.getElementById('carga-eps-status-label'),
              epsStatusMessage: document.getElementById('carga-eps-status-message'),
              wireMain: document.getElementById('carga-wire-main'),
              wireCap: document.getElementById('carga-wire-t-junction-cap'),
              wireDut: document.getElementById('carga-wire-t-junction-dut'),
          };

          if (Object.values(elements).some(el => el === null)) { return; }

          const MAX_EPS_V = 480; const MAX_EPS_I_RATING = 2000;
          const SUT_VOLTAGE_RATIO = 14000 / 480; const I_DUT = 640.0;
          const capVoltageLevels = [3500, 7000, 14000]; const capPowerLevels = [0, 2400, 4800, 7200, 9600, 12000, 14400];
          const state = { powerLevel: 0, capVIndex: 2, capQIndex: 0, powerIntervalId: null };

          function updateSimulation() {
              const V_eps = MAX_EPS_V * (state.powerLevel / 100);
              const V_sut_out = V_eps * SUT_VOLTAGE_RATIO;
              const V_nominal_cap = capVoltageLevels[state.capVIndex];
              const Q_nominal = capPowerLevels[state.capQIndex] * 1000;
              let I_capacitor = 0;
              if (V_sut_out > 0 && V_nominal_cap > 0 && Q_nominal > 0) {
                  const Q_real = Q_nominal * Math.pow(V_sut_out / V_nominal_cap, 2);
                  I_capacitor = Q_real / V_sut_out;
              }
              const I_saida_SUT = (state.powerLevel > 0) ? (I_DUT - I_capacitor) : 0;
              const I_eps = I_saida_SUT * SUT_VOLTAGE_RATIO;
              
              elements.powerLevelDisplay.textContent = state.powerLevel;
              elements.epsVoltage.textContent = V_eps.toFixed(1);
              elements.epsCurrent.textContent = I_eps.toFixed(1);
              elements.sutOutputVoltage.textContent = (V_sut_out / 1000).toFixed(1);
              elements.sutOutputCurrent.textContent = I_saida_SUT.toFixed(1);
              elements.capacitorCurrentDisplay.textContent = I_capacitor.toFixed(1);
              elements.vCapDisplay.textContent = (V_nominal_cap / 1000).toFixed(1) + ' kV';
              elements.qCapDisplay.textContent = capPowerLevels[state.capQIndex] + ' kVAR';
              
              updateWireVisualization(I_eps, I_capacitor, I_saida_SUT);
              updateStatusCard(I_eps);
          }

          function updateWireVisualization(I_eps, I_capacitor, I_saida_SUT) {
              [elements.wireMain, elements.wireCap, elements.wireDut].forEach(el => el.className = 'carga-wire');
              if(state.powerLevel > 0) {
                  elements.wireMain.classList.add('carga-wire-color-source', 'carga-animated-wire');
                  if (I_eps < 0) elements.wireMain.classList.add('reverse');
                  if(I_capacitor > 0.1) elements.wireCap.classList.add('carga-wire-color-cap', 'carga-animated-wire');
                  elements.wireDut.classList.add('carga-wire-color-load', 'carga-animated-wire');
              }
          }

          function updateStatusCard(I_eps) {
              elements.epsStatusCard.className = 'alert';
              const abs_I_eps = Math.abs(I_eps);
              
              if (state.powerLevel === 0) { 
                  elements.epsStatusCard.classList.add('alert-secondary'); 
                  elements.epsStatusLabel.textContent = 'FONTE DESLIGADA'; 
                  elements.epsStatusMessage.textContent = 'Ajuste a potência para iniciar.'; 
              } else if (abs_I_eps > MAX_EPS_I_RATING) { 
                  elements.epsStatusCard.classList.add('alert-danger'); 
                  elements.epsStatusLabel.textContent = 'SOBRECARGA CRÍTICA'; 
                  elements.epsStatusMessage.textContent = `Corrente da fonte (${abs_I_eps.toFixed(0)} A) excedeu o limite de ${MAX_EPS_I_RATING} A.`; 
              } else if (I_eps < 0) { 
                  elements.epsStatusCard.classList.add('alert-warning'); 
                  elements.epsStatusLabel.textContent = 'FLUXO REVERSO (SOBRECOMPENSADO)'; 
                  elements.epsStatusMessage.textContent = 'Capacitor está injetando corrente na fonte.'; 
              } else { 
                  elements.epsStatusCard.classList.add('alert-success'); 
                  elements.epsStatusLabel.textContent = 'OPERAÇÃO NOMINAL'; 
                  const percentComp = Math.min(100, (I_DUT - abs_I_eps / SUT_VOLTAGE_RATIO) / I_DUT * 100).toFixed(0);
                  elements.epsStatusMessage.textContent = `Carga compensada em ~${percentComp}%. Corrente da fonte: ${abs_I_eps.toFixed(0)} A.`; 
              }
          }
          
          function changePower(amount) { state.powerLevel = Math.max(0, Math.min(100, state.powerLevel + amount)); updateSimulation(); }
          function createIndexChanger(key, levelsArray) {
              return function(change) {
                  let newIndex = state[key] + change;
                  if (newIndex >= 0 && newIndex < levelsArray.length) { state[key] = newIndex; updateSimulation(); }
              };
          }
          const changeCapV = createIndexChanger('capVIndex', capVoltageLevels);
          const changeCapQ = createIndexChanger('capQIndex', capPowerLevels);
          function stopInterval() { clearInterval(state.powerIntervalId); }

          elements.btnUp.addEventListener('mousedown', () => { state.powerIntervalId = setInterval(() => changePower(1), 50); });
          elements.btnDown.addEventListener('mousedown', () => { state.powerIntervalId = setInterval(() => changePower(-1), 50); });
          document.addEventListener('mouseup', stopInterval);
          document.addEventListener('mouseleave', stopInterval);
          elements.btnVCapUp.addEventListener('click', () => changeCapV(1));
          elements.btnVCapDown.addEventListener('click', () => changeCapV(-1));
          elements.btnQCapUp.addEventListener('click', () => changeCapQ(1));
          elements.btnQCapDown.addEventListener('click', () => changeCapQ(-1));
          updateSimulation();
      };
      
      VazioSimulator();
      CargaSimulator();
    });
  </script>
</body>
</html>
<script>
        document.addEventListener('DOMContentLoaded', function() {
            // Função para inicializar o gráfico de limites de corrente do EPS
            function initializeEpsCurrentLimitsChart() {
                const ctx = document.getElementById('epsCurrentLimitsChart');
                if (ctx) {
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['Limite Negativo', 'Corrente Estimada', 'Limite Positivo'],
                            datasets: [{
                                label: 'Corrente (A)',
                                data: [-2000, 0, 2000], // Valores de exemplo, serão atualizados dinamicamente
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.6)', // Vermelho para limite negativo
                                    'rgba(54, 162, 235, 0.6)', // Azul para corrente estimada
                                    'rgba(75, 192, 192, 0.6)'  // Verde para limite positivo
                                ],
                                borderColor: [
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(75, 192, 192, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    title: {
                                        display: true,
                                        text: 'Corrente (A)'
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                },
                                title: {
                                    display: true,
                                    text: 'Limites de Corrente do EPS e Corrente Estimada'
                                }
                            }
                        }
                    });
                }
            }

            // Chamar a função de inicialização do gráfico
            initializeEpsCurrentLimitsChart();

            // Adicionar lógica para atualizar o gráfico dinamicamente com dados do backend
            // Isso exigiria integração com o backend (losses_service.py) e um mecanismo para
            // passar os dados calculados para o frontend. Por exemplo, via Dash callbacks.
            // Exemplo de como você atualizaria os dados (isso seria feito via JS no frontend):
            /*
            function updateEpsCurrentLimitsChart(estimatedCurrent) {
                const chart = Chart.getChart('epsCurrentLimitsChart');
                if (chart) {
                    chart.data.datasets[0].data[1] = estimatedCurrent;
                    chart.update();
                }
            }
            // Chame updateEpsCurrentLimitsChart(valor_da_corrente_do_backend) quando os dados estiverem disponíveis
            */
        });
    </script>