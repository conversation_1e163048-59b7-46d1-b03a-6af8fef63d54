<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Perdas em Transformadores - Simulação Interativa Detalhada</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.7/MathJax.js?config=TeX-MML-AM_CHTML"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }
        .tab-container {
            display: flex;
            border-bottom: 2px solid #ddd;
            margin-bottom: 20px;
        }
        .tab-button {
            background-color: #f1f1f1;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 20px;
            transition: 0.3s;
            font-size: 16px;
            font-weight: bold;
            border-radius: 8px 8px 0 0;
            margin-right: 5px;
        }
        .tab-button:hover {
            background-color: #ddd;
        }
        .tab-button.active {
            background-color: #4CAF50;
            color: white;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 0 8px 8px 8px;
            background-color: #fafafa;
        }
        .tab-content.active-content {
            display: block;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .section h3, .section h4 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .formula-box {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .warning-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .parameter-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .parameter-table th,
        .parameter-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .parameter-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .parameter-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Documentação Técnica de Cálculo de Perdas</h1>

        <div class="tab-container">
            <button class="tab-button active" onclick="openTab(event, 'perdas-vazio')">Perdas em Vazio</button>
            <button class="tab-button" onclick="openTab(event, 'perdas-carga')">Perdas em Carga</button>
        </div>

        <div id="perdas-vazio" class="tab-content active-content">
            <h2>Perdas em Vazio (No-Load Losses)</h2>
            
            <div class="section">
                <h3>1. Parâmetros de Entrada</h3>
                <table class="parameter-table">
                    <thead>
                        <tr>
                            <th>Parâmetro</th>
                            <th>Descrição</th>
                            <th>Unidade</th>
                            <th>Variável Python</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>perdas_vazio_ui</td><td>Perdas em vazio de projeto (placa)</td><td>kW</td><td><code>NoLoadLossesInput.perdas_vazio_ui</code></td></tr>
                        <tr><td>peso_nucleo_ui</td><td>Peso do núcleo magnético</td><td>Ton</td><td><code>NoLoadLossesInput.peso_nucleo_ui</code></td></tr>
                        <tr><td>corrente_excitacao_ui</td><td>Corrente de excitação nominal</td><td>%</td><td><code>NoLoadLossesInput.corrente_excitacao_ui</code></td></tr>
                        <tr><td>inducao_ui</td><td>Indução magnética nominal</td><td>T</td><td><code>NoLoadLossesInput.inducao_ui</code></td></tr>
                        <tr><td>frequencia</td><td>Frequência nominal de operação</td><td>Hz</td><td><code>NoLoadLossesInput.frequencia</code></td></tr>
                        <tr><td>tensao_bt_kv</td><td>Tensão nominal do enrolamento de BT</td><td>kV</td><td><code>NoLoadLossesInput.tensao_bt_kv</code></td></tr>
                        <tr><td>tensao_terciario_kv</td><td>Tensão nominal do enrolamento Terciário (opcional)</td><td>kV</td><td><code>NoLoadLossesInput.tensao_terciario_kv</code></td></tr>
                    </tbody>
                </table>
            </div>

            <div class="section">
                <h3>2. Tabelas de Aço e Interpolação</h3>
                <p>O sistema usa interpolação bilinear para obter valores precisos de perdas (W/kg) e potência magnetizante (VAr/kg) das tabelas de referência do aço (M4 e H110-27). Para o aço H110-27, quando a frequência de teste não é 50Hz ou 60Hz, o sistema usa extrapolação baseada na <strong>Lei de Steinmetz Modificada</strong>:</p>
                <div class="formula-box">
                    <h4>Lei de Steinmetz Modificada para H110-27:</h4>
                    <p><strong>Para Perdas:</strong> $$P \propto f^{1.3} \times B^{2.0}$$</p>
                    <p><strong>Para Potência Magnetizante:</strong> $$P_m \propto f^{1.2} \times B^{1.8}$$</p>
                </div>
            </div>

            <div class="section">
                <h3>3. Análise do Sistema de Teste (SUT/EPS)</h3>
                <h4>3.1. Decisão de Uso do Terciário</h4>
                <p>O sistema verifica se as tensões de teste (1.0pu, 1.1pu, 1.2pu da tensão BT) excedem os limites do SUT (Step-Up Transformer). Se alguma tensão exceder e um enrolamento terciário estiver disponível, ele será usado como fonte para **todos os níveis de teste**. Caso contrário, se o limite for excedido e o terciário não estiver disponível, o teste é considerado CRÍTICO.</p>

                <h4>3.2. Análise de Taps do SUT</h4>
                <p>Para os 5 taps do SUT mais próximos da tensão de teste, calcula-se a corrente refletida no EPS (Electronic Power Supply):</p>
                <div class="formula-box">
                    <p><strong>Corrente refletida no EPS:</strong> $$I_{EPS} = I_{DUT} \times \frac{V_{SUT,AT}}{V_{SUT,BT}}$$</p>
                    <p>Onde \\(I_{DUT}\\) é a corrente de excitação no transformador em teste. A corrente \\(I_{EPS}\\) é então comparada com os limites do equipamento para determinar a viabilidade.</p>
                </div>
            </div>
        </div>

        <div id="perdas-carga" class="tab-content">
            <h2>Perdas em Carga (Load Losses)</h2>

            <div class="section">
                <h3>1. Parâmetros de Entrada</h3>
                <table class="parameter-table">
                    <thead>
                        <tr><th>Parâmetro</th><th>Descrição</th><th>Unidade</th><th>Variável Python</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>perdas_carga_kw_u_nom</td><td>Perdas em carga no tap nominal</td><td>kW</td><td><code>LoadLossesInput.perdas_carga_kw_u_nom</code></td></tr>
                        <tr><td>perdas_carga_kw_u_min</td><td>Perdas em carga no tap mínimo</td><td>kW</td><td><code>LoadLossesInput.perdas_carga_kw_u_min</code></td></tr>
                        <tr><td>perdas_carga_kw_u_max</td><td>Perdas em carga no tap máximo</td><td>kW</td><td><code>LoadLossesInput.perdas_carga_kw_u_max</code></td></tr>
                        <tr><td>potencia_mva</td><td>Potência nominal do transformador</td><td>MVA</td><td><code>LoadLossesInput.potencia_mva</code></td></tr>
                        <tr><td>impedancia</td><td>Impedância percentual nominal</td><td>%</td><td><code>LoadLossesInput.impedancia</code></td></tr>
                        <tr><td>tensao_at_kv</td><td>Tensão nominal do enrolamento de AT</td><td>kV</td><td><code>LoadLossesInput.tensao_at_kv</code></td></tr>
                        <tr><td>temperatura_referencia</td><td>Temperatura de referência das perdas</td><td>°C</td><td><code>LoadLossesInput.temperatura_referencia</code></td></tr>
                        <tr><td>perdas_vazio_kw_calculada</td><td>Perdas em vazio calculadas (do módulo anterior)</td><td>kW</td><td><code>LoadLossesInput.perdas_vazio_kw_calculada</code></td></tr>
                    </tbody>
                </table>
            </div>

            <div class="section">
                <h3>2. Cenários de Teste</h3>
                <p>O sistema calcula os parâmetros para diversos cenários de teste em cada tap (Nominal, Menor, Maior):</p>
                <ul>
                    <li><strong>25°C:</strong> Simula o ensaio a frio.</li>
                    <li><strong>Frio:</strong> Simula o ensaio com perdas na temperatura de referência.</li>
                    <li><strong>Quente:</strong> Simula o ensaio com perdas ajustadas.</li>
                    <li><strong>Sobrecarga (1.2 pu, 1.4 pu):</strong> Simula condições de sobrecarga (apenas para \\(V_{AT} \\ge 230kV\\)).</li>
                </ul>
                <p>Para cada cenário, são calculados \\(V_{teste}\\), \\(I_{teste}\\), Potência Ativa (\\(P_{ativa}\\)) e Potência Reativa (\\(Q_{teste}\\)).</p>
            </div>
            
            <div class="section">
                <h3>3. Lógica do Banco de Capacitores e Otimização da Corrente EPS</h3>
                <p>O objetivo do banco de capacitores é compensar a potência reativa (\\(Q_{teste}\\)) do transformador, **minimizando a corrente resultante no EPS**. A lógica é a seguinte:</p>

                <h4>3.1. Seleção da Tensão do Banco</h4>
                <p>Para cada cenário, o sistema seleciona duas possíveis tensões de banco:</p>
                <ul>
                    <li><strong>V≤ (Sem Fator - S/F):</strong> A menor tensão de banco disponível que seja **maior ou igual** à \\(V_{teste}\\).</li>
                    <li><strong>V> (Com Fator - C/F):</strong> A maior tensão de banco disponível que seja **menor** que a \\(V_{teste}\\), mas que a suporte com um fator de sobretensão (ex: 110%).
                        $$ V_{teste} \le V_{banco, C/F} \times 1.1 $$
                    </li>
                </ul>

                <h4>3.2. Nomenclatura das Unidades Capacitivas e Grupos</h4>
                <p>As unidades de capacitores seguem a nomenclatura **CPxAy**, onde:</p>
                <ul>
                    <li>**x:** Posição física em série (1 a 4).</li>
                    <li>**A:** Fase (A, B, C).</li>
                    <li>**y:** **Grupo Lógico** (1 ou 2). O grupo é definido pelo último dígito.</li>
                </ul>

                <h4>3.3. Lógica de Otimização (Trifásico)</h4>
                <div class="warning-box">
                    <strong>Objetivo Principal:</strong> Encontrar a configuração de strings de capacitores (balanceadas por fase) e chaves Q que resulta na **menor corrente absoluta no EPS**, mantendo a potência reativa fornecida suficiente para o teste.
                </div>
                <ol>
                    <li>O sistema gera uma lista de todas as configurações de strings balanceadas possíveis (1 por fase, 2 por fase, etc.), ordenadas por tamanho.</li>
                    <li>Para cada configuração de strings, testa-se todas as combinações de chaves Q.</li>
                    <li>Cria-se uma lista de "soluções candidatas" que fornecem potência reativa suficiente (\\(Q_{efetiva} \ge Q_{teste}\\)).</li>
                    <li>Para cada candidata, calcula-se a corrente resultante no EPS:
                        $$ I_{EPS} = (I_{teste} - I_{capacitiva}) \times \frac{V_{SUT,AT}}{V_{SUT,BT}} $$
                    </li>
                    <li>A solução que resultar no valor de \\(|I_{EPS}|\\) mais próximo de zero é escolhida como a melhor.</li>
                </ol>

                <h4>3.4. Lógica de Seleção de Grupo (Grupo 1 vs. Grupo 1+2)</h4>
                <p>O sistema executa a lógica de otimização duas vezes: uma vez usando apenas unidades do Grupo 1, e outra usando unidades dos Grupos 1 e 2. A decisão final segue esta prioridade:</p>
                <ol>
                    <li>Se ambas as soluções (G1 e G1+2) são válidas (\\(I_{EPS}\\) dentro dos limites), escolhe-se a que minimiza \\(|I_{EPS}|\\).</li>
                    <li>Se apenas uma solução é válida, ela é escolhida.</li>
                    <li>Se nenhuma é válida, escolhe-se a que chega mais perto de atender aos limites (a "menos errada").</li>
                </ol>

                <h4>3.5. Correção da Corrente EPS para Monofásico</h4>
                <p>Para ensaios em transformadores monofásicos, a corrente calculada no EPS é multiplicada por um fator de \\(\\sqrt{3}\\) conforme recomendação técnica para simular a carga correta no sistema de teste trifásico.</p>
                $$ I_{EPS, mono} = I_{EPS, calc} \times \sqrt{3} $$
            </div>
        </div>
    </div>

    <script>
        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
                tabcontent[i].classList.remove("active-content");
            }
            tablinks = document.getElementsByClassName("tab-button");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }
            document.getElementById(tabName).style.display = "block";
            document.getElementById(tabName).classList.add("active-content");
            evt.currentTarget.className += " active";
        }

        document.addEventListener('DOMContentLoaded', (event) => {
            document.getElementsByClassName('tab-button')[0].click();
            if (window.MathJax) {
                MathJax.Hub.Queue(["Typeset", MathJax.Hub]);
            }
        });
    </script>
</body>
</html>